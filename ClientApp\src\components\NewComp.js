import axios from "axios";

const GetApiResponse = async (url, obj) => {
  let apiReturn = null;
  try {
    const bodyFormData = new FormData();
    bodyFormData.append("sbr", JSON.stringify(obj));
    const formObj = await axios({
      method: "post",
      url: process.env.API_URL + url,
      data: bodyFormData,
      headers: { "Content-Type": "multipart/form-data" },
    });
    const res = await formObj.data;
    if (
      res.ReturnMessage.toUpperCase() === "SUCCESS" ||
      res.ReturnMessage === "" ||
      res.ReturnMessage === "CollegeRequired" ||
      res.ReturnMessage.toUpperCase() === "APPLICATIONPAID" ||
      res.ReturnMessage.toUpperCase() === "PAYMENTERROR" ||
      res.ReturnMessage.toUpperCase() === "FPCNOTPROVIDED"
    ) {
      apiReturn = {
        status: "SUCCESS",
        data: res,
      };
    } else if (res.ReturnMessage.toUpperCase() === "SERVERERROR") {
      apiReturn = {
        status: "SERVERERROR",
        data: "Server error has occured.",
      };
    } else if (
      res.ReturnMessage.toUpperCase() === "FAILED" ||
      res.ReturnMessage.toUpperCase() === "FAILEDVALIDATION"
    ) {
      apiReturn = { status: "FAILED", data: res };
    }
  } catch (err) {
    console.log("URL:" + process.env.API_URL + url);
    console.log(obj);
    apiReturn = {
      status: "SERVERERROR",
      data: "Server error has occured.",
    };
  } finally {
    return apiReturn; // Could be success or error
  }
};
export async function GetPdfUrl(url, obj) {
  const bodyFormData = new FormData();
  bodyFormData.append("sbr", JSON.stringify(obj));
  await axios({
    method: "post",
    url: process.env.PDF_URL,
    data: bodyFormData,
    headers: { "Content-Type": "multipart/form-data" },
    responseType: "blob",
  })
    .then((response) => {
      const file = new Blob([response.data], { type: "application/pdf" });
      //Build a URL from the file
      const fileURL = URL.createObjectURL(file);
      //Open the URL on new Window
      const pdfWindow = window.open();
      pdfWindow.location.href = fileURL;
    })
    .catch((error) => {
      console.log(error);
    });
}
export async function SaveCertForm(url, userForm, answersObj) {
  const Questions = !answersObj ? [] : GetQuestions(answersObj);
  const postForm = { ...userForm, Questions };
  const response = await GetApiResponse(url, postForm);
  return response;
}
export async function getSaveCert(url, userForm) {
  const response = await GetApiResponse(url, userForm);
  return response;
}
export async function saveAppReview(userForm) {
  const response = await GetApiResponse("GetAppSectionReview", userForm);
  return response;
}
export async function saveFormReview(userForm) {
  const response = await GetApiResponse("GetSubmitFormReview", userForm);
  return response;
}
export async function saveFormPage(
  userForm,
  answersObj
) {
  const Questions = !answersObj ? [] : GetQuestions(answersObj);
  const postForm = { ...userForm, Questions };
  const response = await GetApiResponse("PostFormPageSave", postForm);
  return response;
}
export async function saveFormPageMultiple(url, userForm) {
  const response = await GetApiResponse(url, userForm);
  return response;
}
const formatCurrency = (val) => {
  return val.replace(/,/g, "").split(".")[0];
};
const GetQuestions = (answersObj) => {
  let arr = [];
  let val = "";
  for (const [key, value] of Object.entries(answersObj)) {
    val = value;
    const obj = {
      Id: key,
      Response: val,
    };
    arr.push(obj);
  }
  return arr;
};
export function checklistPageFromJSON(formPageJSON) {
  const questions = extractCardFields(formPageJSON);
  return {
    title: formPageJSON.PageHeading,
    text: formPageJSON.PageText,
    footHeading: formPageJSON.PageFootHeading,
    footText: formPageJSON.PageFootText,
    isCompleted: formPageJSON.AppCompleted,
    review: formPageJSON.PageFootSubmit,
    questions,
  };
}
export function extractCardFields(formPageJSON) {
  const pageTextField = [];
  formPageJSON.FWMenuList.map((el) => {
    if (el.Disabled !== "Y") {
      pageTextField.push(el);
    }
  });
  return pageTextField || null;
}
export function finalChargeJSON(formPageJSON) {
  const pageTextField = formPageJSON.FWFormItemList;
  return {
    title: formPageJSON.PageHeading,
    appFee: formPageJSON.ApplicationFee,
    totalCharge: formPageJSON.TotalCharge,
    amountDue: formPageJSON.AmountDue,
    text: formPageJSON.PageText,
    items: pageTextField,
  };
}
export function dashboardJSON(formPageJSON) {
  const pageTextField = formPageJSON.FWFormItemList;
  return {
    title: formPageJSON.PageHeading,
    text: formPageJSON.PageText,
    items: pageTextField,
  };
}
export function sectionReviewPageFromJSON(formPageJSON) {
  const pageTextField = formPageJSON.FWFormItemList;
  return {
    title: formPageJSON.PageHeading,
    items: pageTextField,
  };
}
export function multiplePageFromJSON(obj, dicts, chck) {
  const FWFormItemList = obj?.FWFormItemList;
  const questionAnswers = chck ? extractQuestionAnswers(FWFormItemList) : null;
  return {
    SectionHeading: obj.SectionHeading,
    ItemDesc: obj.ItemDescription,
    heading: extractFormPageText(obj),
    ItemNumber: obj.ItemNumber,
    dicts: dicts,
    questionAnswers,
    FWFormItemList,
  };
}
export function formPageFromJSON(formPageJSON) {
  const questions = extractFormPageQuestions(formPageJSON);
  //const questions = requiredHiddenFields(quest)
  const questionAnswers = extractQuestionAnswers(questions);

  return {
    name: formPageJSON.PageName,
    heading: extractFormPageText(formPageJSON),
    title: formPageJSON.PageHeading,
    headingText: formPageJSON.SectionHeading,
    dicts: formPageJSON.FWDictsList.Dicts,
    questionAnswers,
    questions,
    isReviewPage: formPageJSON?.ReviewPage || "",
    pageText: formPageJSON?.PageText || "",
  };
}
function extractFormPageText(formPageJSON) {
  // I'm assuming there will be only one "text" item in each page.
  const pageTextField = formPageJSON.FWFormItemList.find(
    (item) => item.itemType === "text"
  );
  return pageTextField || null;
}

function extractFormPageQuestions(formPageJSON) {
  return formPageJSON.FWFormItemList.filter((item) => {
    if (item.itemType === "question" || item.itemType === "button") {
      return item;
    }
  });
}
export function multiRequiredHiddenFields(js) {
  js.FWFormItemList.map((question) => {
    if (question.itemType !== "text") {
      if (
        question.conditionalRequireQuestions &&
        question.conditionalRequireQuestions.length > 0
      ) {
        question.conditionalRequireQuestions.map((r) => {
          let arr = r.condReqParVal.split(";");
          let cond = r.condOperator;
          js?.FWFormItemList.map((q) => {
            if (r.requiredQuestionId === q.id) {
              if (cond === "=") {
                if (arr.includes(question.questionValue)) {
                  q.required = "Y";
                } else {
                  q.required = "N";
                }
              } else if (cond === ">") {
                if (arr.includes(question.questionValue)) {
                  q.required = "Y";
                } else {
                  q.required = "N";
                }
              }
            }
          });
        });
      }

      if (
        question.conditionalDisableQuestions &&
        question.conditionalDisableQuestions.length > 0
      ) {
        question.conditionalDisableQuestions.map((d) => {
          let empty = "";
          let notin = "";
          if (d.condDisableParValEQ) {
            empty = d.condDisableParValEQ.split(";");
          }
          if (d.condDisableParValNOTEQ) {
            notin = d.condDisableParValNOTEQ.split(";");
          }
          js?.FWFormItemList.map((o) => {
            if (d.disableQuestionId === o.id) {
              if (empty && empty.includes(question.questionValue)) {
                o.hideElem = true;
              } else if (notin && !notin.includes(question.questionValue)) {
                o.hideElem = true;
              } else {
                o.hideElem = false;
              }
            }
          });
        });
      }
    }
  });
  return js;
}
export function requiredHiddenFields(js) {
  js.questions.map((question) => {
    if (question.itemType !== "text") {
      if (
        question.conditionalRequireQuestions &&
        question.conditionalRequireQuestions.length > 0
      ) {
        question.conditionalRequireQuestions.map((r) => {
          let arr = r.condReqParVal.split(";");
          let cond = r.condOperator;
          js?.questions.map((q) => {
            if (r.requiredQuestionId === q.id) {
              if (cond === "=") {
                if (arr.includes(question.questionValue)) {
                  q.required = "Y";
                } else {
                  q.required = "N";
                }
              } else if (cond === ">") {
                if (arr.includes(question.questionValue)) {
                  q.required = "Y";
                } else {
                  q.required = "N";
                }
              }
            }
          });
        });
      }

      if (
        question.conditionalDisableQuestions &&
        question.conditionalDisableQuestions.length > 0
      ) {
        question.conditionalDisableQuestions.map((d) => {
          let empty = "";
          let notin = "";
          if (d.condDisableParValEQ) {
            empty = d.condDisableParValEQ.split(";");
          }
          if (d.condDisableParValNOTEQ) {
            notin = d.condDisableParValNOTEQ.split(";");
          }
          js?.questions.map((o) => {
            if (d.disableQuestionId === o.id) {
              if (empty && empty.includes(question.questionValue)) {
                o.hideElem = true;
              } else if (notin && !notin.includes(question.questionValue)) {
                o.hideElem = true;
              } else {
                o.hideElem = false;
              }
            }
          });
        });
      }
    }
  });
  // formData.map(el => {
  //   if (
  //     el.conditionalRequireQuestions &&
  //     el.conditionalRequireQuestions.length > 0
  //   ) {
  //     el.conditionalRequireQuestions.map(r => {
  //       let arr = r.condReqParVal.split(";")
  //       let cond = r.condOperator
  //       formData.map(q => {
  //         if (r.requiredQuestionId === q.id) {
  //           if (cond === "=") {
  //             if (arr.includes(el.questionValue)) {
  //               q.required = "Y"
  //             } else {
  //               q.required = "N"
  //             }
  //           } else if (cond === ">") {
  //             if (arr.includes(el.questionValue)) {
  //               q.required = "Y"
  //             } else {
  //               q.required = "N"
  //             }
  //           }
  //         }
  //       })
  //     })
  //   }
  //   if (
  //     el.conditionalDisableQuestions &&
  //     el.conditionalDisableQuestions.length > 0
  //   ) {
  //     el.conditionalDisableQuestions.map(dis => {
  //       let empty = dis.condDisableParValEQ.split(";")
  //       let notin = dis.condDisableParValNOTEQ.split(";")
  //       formData.map(ols => {
  //         ols.hideElem = false
  //         if (dis.disableQuestionId === ols.id) {
  //           if (
  //             empty.includes(el.questionValue) ||
  //             notin.includes(el.questionValue)
  //           ) {
  //             ols.hideElem = true
  //           }
  //         }
  //       })
  //     })
  //   }
  // })
  return js;
}

function extractQuestionAnswers(questions) {
  const answers = {};

  questions?.forEach((question) => {
    if (question.itemType === "question" && !question.hideElem) {
      answers[question.id] = question.questionValue;
    }
  });
  return answers;
}
