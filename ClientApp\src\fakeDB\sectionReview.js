export const dtjson = [
  {
    PageName: "Correction",
    PageHeading: "Correction",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "<p>Please choose the main reason you need to correct or update your CSS Profile application.  If you have more than one reason, you can explain in the box below.  If you are adding parent financial information, please select that option.</p>",
      },
      {
        itemType: "question",
        id: "CorrectionReasonId",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "card",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<ul><li>When you completed your CSS Profile you may have answered some questions incorrectly, such as date of birth or whether you have legal dependents.  That meant the CSS Profile did not ask you all the questions about your parents.</li></ul>",
        helpStyle: "Expandable Hint",
        helpTitle: "Why would I need to add parent financial information?",
        textContent: "Enter Reason for Correction",
      },
      {
        itemType: "question",
        id: "CorrectionExplanation",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "textarea",
        size: "40",
        minLength: "",
        maxLength: "2000",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<span>What information on your CSS Profile do you need to update?  You can include any information you need to correct or any new information regarding your family's financial situation.  Be sure to include as much detail as possible, including dollar amounts, dates of changes, etc.  Your institutions may request additional information based on what you share here.</span><p>You may provide up to 2,000 characters of information.</p><p>Please provide the information in English using the following standard American keyboard characters: letters (A-Z), numbers (0-9) and punctuation (.,:;-!$@#=).  Note: ampersands, greater than/less than symbols, or other characters may cause processing problems</p><p>The information you enter will be provided to all the colleges and programs you select to receive your information.  If you have information specific to an institution, or if you need to provide additional information, please send it directly to the appropriate colleges and programs.</p>",
        helpStyle: "Notice",
        helpTitle: "",
        textContent: "Enter additional details for Correction",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "card",
          Name: "card",
          DictItems: [
            {
              Value: "1",
              Display: "Need to add Parent Financial Information",
              Order: "001",
            },
            // {
            //   Value: "2",
            //   Display: "Need to add a Business or Farm",
            //   Order: "002",
            // },
            {
              Value: "3",
              Display: "Made a mistake",
              Order: "003",
            },
            // {
            //   Value: "4",
            //   Display: "Change in Employment",
            //   Order: "004",
            // },
            // {
            //   Value: "5",
            //   Display: "COVID-19",
            //   Order: "005",
            // },
            // {
            //   Value: "6",
            //   Display: "Medical Dental Expenses",
            //   Order: "006",
            // },
            // {
            //   Value: "7",
            //   Display: "Catastrophic Event",
            //   Order: "007",
            // },
            // {
            //   Value: "8",
            //   Display: "Elder Care",
            //   Order: "008",
            // },
            // {
            //   Value: "9",
            //   Display: "Nonrecurring Income",
            //   Order: "009",
            // },
            // {
            //   Value: "10",
            //   Display: "Financial Support",
            //   Order: "010",
            // },
            {
              Value: "11",
              Display: "Other",
              Order: "011",
            },
          ],
        },
      ],
      Count: 1,
    },
  },
  {
    PageName: "CodePayment",
    PageHeading: "Fee Payment Codes",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "<p>Some colleges and scholarship programs distribute Fee payment Codes to their applicants. If you have Fee Payment Code(s) and select the College or Program you received the code from below.</p><p>This is your only opportunity to enter Fee Payment Codes before sumbitting your application.</p><p><b>Please Note: Refunds will not be issued for fees paid prior to submitting payment codes.</b></p>",
      },
      {
        itemType: "question",
        id: "f_stu_first_name",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "checkbox",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "If you are using a wild code for College of Sampley(old rollover), please check the box above.",
        helpStyle: "Notice",
        helpTitle: "",
        textContent: "College of Sample(old rollover) wild code?",
      },
      {
        itemType: "question",
        id: "f_stu_first_nameCheck",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "College of Sample(old rollover)",
      },
      {
        itemType: "question",
        id: "f_stu_first_namedddd",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "radio",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "If you are using a wild code for Sample University(old rollover), please select Yes above.",
        helpStyle: "Notice",
        helpTitle: "",
        textContent: "Sample University(old rollover) wild code?",
      },
      {
        itemType: "question",
        id: "f_stu_middle_name",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Sample University(old rollover)",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "card",
          Name: "card",
          DictItems: [
            {
              Value: "Visa",
              Display: "Visa",
              Order: "002",
            },
            {
              Value: "MasterCard",
              Display: "Master Card",
              Order: "003",
            },
            {
              Value: "AmericanExpress",
              Display: "American Express",
              Order: "004",
            },
          ],
        },
        {
          Display: "YesNo_YN",
          Name: "YesNo_YN",
          DictItems: [
            {
              Value: "Y",
              Display: "Yes",
              Order: "001",
            },
            {
              Value: "N",
              Display: "No",
              Order: "002",
            },
          ],
        },
      ],
      Count: 2,
    },
  },
  {
    PageName: "CreditDebit",
    PageHeading: "Credit/Debit Card Payment",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          '<p>Enter the information below to pay for your CSS Profile by credit or debit card. Please be sure your application is fully ready to be submitted and then click "Submit Payment" below to submit your payment information and application for processing.</p>',
      },
      {
        itemType: "question",
        id: "CardFirstname",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "First Name of Cardholder",
      },
      {
        itemType: "question",
        id: "CardLastname",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Last Name of Cardholder",
      },
      {
        itemType: "question",
        id: "CardType",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "card",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Credit Card Type",
      },
      {
        itemType: "question",
        id: "CardNumber",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "14",
        maxLength: "16",
        format: "number",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Card Number",
      },
      ,
      {
        itemType: "question",
        id: "month_year",
        ariaDescribedby: "month_year",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "date",
        displayFormat: "mm/yyyy",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Expiration Date",
      },
      ,
      {
        itemType: "question",
        id: "CardSecCode",
        ariaDescribedby: "CardSecCode",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "3",
        maxLength: "4",
        format: "number",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "Code is located on the back of the card and is three to four digits long",
        helpStyle: "Notice",
        helpTitle: "",
        textContent: "Security Code",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "card",
          Name: "card",
          DictItems: [
            {
              Value: "MC",
              Display: "Master Card",
              Order: "001",
            },
            {
              Value: "VI",
              Display: "Visa",
              Order: "002",
            },
            {
              Value: "AX",
              Display: "American Express",
              Order: "003",
            },
            ,
            {
              Value: "DI",
              Display: "Discover",
              Order: "004",
            },
            {
              Value: "DC",
              Display: "Diner's Club",
              Order: "005",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    ReturnMessage: "",
    Username: "<EMAIL>",
    UserId: "PdcbBmhDEcMlmz$/dgwmaKGu3TYhnRecLq1D1ju6lK85VMIz6/wWWA==",
    AwardYear: "2024",
    ApplicationId: "n5qz02ASpQ$T3AgVFPNeJX1g//BmggvM829FvrjIsNJqIkNZ6em$7A==",
    OrgFormId: "lv35$iih2vBQU3GjoLob9mK7GGP2XJOhnRfIauWMICXp1LIUI15J2g==",
    PageName: "FinalCharge",
    FormId: "CB000008141",
    CBFinAidID: "",
    PageHeading: "Final Charge Summary",
    ApplicationFee: "9.00",
    TotalCharge: "$41",
    AmountDue: "$41",
    FWFormItemList: [
      {
        code: "5024",
        name: "College of Sample(old rollover)",
        amount: "16",
      },
      {
        code: "5025",
        name: "Sample University(old rollover)",
        amount: "16",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
  {
    ReturnMessage: "",
    Username: "<EMAIL>",
    UserId: "PdcbBmhDEcMlmz$/dgwmaKGu3TYhnRecLq1D1ju6lK85VMIz6/wWWA==",
    AwardYear: "2024",
    ApplicationId: "n5qz02ASpQ$T3AgVFPNeJX1g//BmggvM829FvrjIsNJqIkNZ6em$7A==",
    OrgFormId: "lv35$iih2vBQU3GjoLob9mK7GGP2XJOhnRfIauWMICXp1LIUI15J2g==",
    PageName: "Certification",
    FormId: "CB000008141",
    CBFinAidID: "",
    PageHeading: "Certification",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "All the information on this application is true and complete to the best om my knowledge. All the information on this application is true and complete to the best om my knowledge. I agree..............",
      },
      {
        itemType: "question",
        id: "f_stu_ward_court_cur",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "checkbox",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "f_stu_ward_court_curMsg",
        conditionalRequireQuestions: [],
        conditionalDisableQuestions: [],
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "I have read, understand, and agree to the statement above",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
];

export const dashboard = [
  {
    PageName: "Dashboard",
    PageHeading: "Dashboard",
    PageText: "<p>Welcome, Amanda!</p><p>Your CBFinAid ID: <b>ZAAR89UF</b></p>",
    FWFormItemList: [
      {
        PageName: "",
        MenuName: "",
        PageURL: "checkList",
        PageHeading: "Application Status",
        PageText:
          "<p>Display Application Status:</p><p>Start/In Progress/Complete</b></p>",
        ButtonText: "Continue Application",
      },
      {
        PageName: "",
        PageURL: "",
        MenuName: "",
        PageHeading: "Fee Waiver Eligible",
        PageText: "<p>Display Eligible Status</p>",
      },
      {
        PageName: "AcademicInfo4",
        PageURL: "program",
        MenuName: "AcademicInfo",
        PageHeading: "Colleges & Program",
        PageText:
          "<p>5024 College of Sample(old rollover) MA</p><p>5025 Sample University(old rollover) MA</p>",
        ButtonText: "Add a College or Program",
      },
      {
        PageName: "Correction",
        PageURL: "payment",
        MenuName: "",
        PageHeading: "CSS Profile Correction",
        PageText: "<p>Correct Your CSS Profile</p>",
        ButtonText: "CSS Profile Correction",
      },
      {
        PageName: "",
        PageURL: "",
        MenuName: "",
        PageHeading: "Next Steps",
        PageText: "<p>Display Next Step Info here</p>",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
];

export const sectionReview = [
  {
    PageName: "GettingStarted",
    PageHeading: "Student Info Review",
    PageText:
      "Review the following information. If the information is accurate and correct, choose “Save and Continue” at the bottom of the page.",
    ReviewPage: "Getting Started",
    FWFormItemList: [
      {
        PageName: "StudentInfo1",
        PageHeading:
          "First, we'll collect some basic information about you, the student.",
        FWFormItemList: [
          {
            itemType: "question",
            id: "f_stu_first_name",
            questionValue: "Mary",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "text",
            size: "40",
            minLength: "",
            maxLength: "35",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent: "First name",
          },
          {
            itemType: "question",
            id: "f_stu_middle_name",
            questionValue: "",
            required: "N",
            requiredErrMsg: "",
            fieldType: "text",
            size: "40",
            minLength: "",
            maxLength: "35",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent: "Middle name",
          },
          {
            itemType: "question",
            id: "f_stu_last_name",
            questionValue: "Test",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "text",
            size: "40",
            minLength: "",
            maxLength: "35",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent: "Last name",
          },
          {
            itemType: "question",
            id: "f_stu_preferred_name",
            questionValue: "",
            required: "N",
            requiredErrMsg: "",
            fieldType: "text",
            size: "40",
            minLength: "",
            maxLength: "35",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent:
              "<b>Preferred Name</b> - The preferred name you enter here will be shared with the colleges you select within the application and can be seen <b>by your parent(s)/guardian(s) who have access to your CSS Profile.</b>  <br /><br />If you would like College Board to use your preferred name outside of CSS Profile, you will need to updated your College Board account.",
            helpStyle: "Notice",
            helpTitle: "",
            textContent: "Preferred name",
          },
        ],
      },
      {
        PageName: "StudentInfo2",
        PageHeading: "What is Mary's contact information?",
        FWFormItemList: [
          {
            itemType: "question",
            id: "f_stu_phone",
            questionValue: "**********",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "text",
            size: "15",
            minLength: "10",
            maxLength: "15",
            format: "number",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent: "Phone number",
          },
          {
            itemType: "question",
            id: "f_stu_email",
            questionValue: "<EMAIL>",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "text",
            size: "45",
            minLength: "",
            maxLength: "50",
            format: "email",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent:
              "<b>Email Address</b> - Use an email address you check regularly so we can update you on the status of your application and so schools can reach you.<br /><br />Be sure the student\\'s email address can receive email from the College Board by adding our domain name, collegeboard.org, to the list of enabled mail senders. <br />",
            helpStyle: "Notice",
            helpTitle: "",
            textContent: "Email address",
          },
        ],
      },
      {
        PageName: "StudentInfo3",
        PageHeading:
          "Provide additional information about yourself, Mary. This helps us figure out what other questions we should ask.",
        FWFormItemList: [
          {
            itemType: "question",
            id: "f_stu_dob",
            questionValue: "07/12/2002",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "text",
            size: "10",
            minLength: "",
            maxLength: "10",
            format: "date",
            displayFormat: "mm/dd/yyyy",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg:
              "Error: The date of birth you entered is either an invalid date or does not fall within the valid date range.  Please re-enter.",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent: "Date of birth",
          },
          {
            itemType: "question",
            id: "f_stu_mar_status",
            questionValue: "Single",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "select",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "StudentMaritalStatus",
            checkboxCheckedValue: "",
            questionMsg: "f_stu_mar_statusMsg",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent:
              "<b>Marital Status</b> - Make sure to answer for the student\\'s marital status.<br />",
            helpStyle: "Notice",
            helpTitle: "",
            textContent: "Mary's marital status",
          },
        ],
      },
      {
        PageName: "StudentInfo4",
        PageHeading:
          "Indicate if any of these are true for Mary. These questions help us understand your situation better.",
        FWFormItemList: [
          {
            itemType: "question",
            id: "f_stu_legal_dependents",
            questionValue: "No",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "radio",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "YesNo_YN",
            checkboxCheckedValue: "",
            questionMsg: "f_stu_legal_dependentsMsg",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent:
              '<b>Legal Dependents </b> - Select "Yes" only if:  <br />  <br /> - the student has children for whom the student provides most (more than 50%) of their support or <br /> <br /> - the student provides most of the support (more than 50%) for people who live with the student (other than the student\\\'s spouse) and will continue to provide this level of support during the 2022-23 academic year. <br />',
            helpStyle: "Expandable Hint",
            helpTitle: "What is a legal dependent?",
            textContent: "Mary has legal dependents",
          },
          {
            itemType: "question",
            id: "f_stu_legal_dependents_married",
            questionValue: "",
            required: "Yes",
            requiredErrMsg: "",
            fieldType: "radio",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "YesNo_YN",
            checkboxCheckedValue: "",
            questionMsg: "f_stu_legal_dependentsMsg",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent:
              "<b>Legal Dependents (Married) </b> - A legal dependent is someone who receives more than 50% of their support from you, the student.  This can be a child or another person (other than your spouse) who lives with you, and will continue to receive this level of support for the 2023-24 school year. <br />",
            helpStyle: "Expandable Hint",
            helpTitle: "What is a legal dependent?",
            textContent:
              "Mary has legal dependents (not including their spouse)",
          },
          {
            itemType: "question",
            id: "f_stu_vet_status",
            questionValue: "No",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "radio",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "YesNo_YN",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "f_stu_citizenship_status",
            condDsblParVal: ";BLANK;3;4",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "f_stu_citizenship_status",
            condReqParVal: ";1;2;",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent:
              "<b>Veteran Status </b> - ROTC students, students at service academies, and National Guard/Reserves enlistees are not considered veterans. <br />",
            helpStyle: "Expandable Hint",
            helpTitle: "What is a legal dependent?",
            textContent:
              "Mary is a veteran of the U.S. Armed Forces or currently serving on active duty",
          },
          {
            itemType: "question",
            id: "StuEmancipated",
            questionValue: "No",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "radio",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "YesNo_YN",
            checkboxCheckedValue: "",
            questionMsg: "StuEmancipatedMsg",
            condDsblChild: "",
            condDsblParFld: "f_stu_citizenship_status",
            condDsblParVal: ";BLANK;3;",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "f_stu_citizenship_status",
            condReqParVal: ";1;2;4;",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent:
              "<b>Emanicipated Minor  </b> - Answer \"Yes\" if the student is legally emancipated, that is the relationship between the student and their biological or adoptive parents has been terminated and the student is free to make legal and personal decisions on their own behalf, and you can provide a copy of a court\\'s decision that the student is today, or was until the age of being an adult in the student\\'s state, an emancipated minor.  The court issuing the decision must have been located in the student\\'s state of legal residence at the time the court\\'s decision was issued. <br /> <br />Financial aid administrators may require a copy of the court\\'s decision. <br />",
            helpStyle: "Expandable Hint",
            helpTitle: "Tell me more",
            textContent:
              "Mary was determined to be an emancipated minor by a court in their state of legal residence",
          },
          {
            itemType: "question",
            id: "q_stu_homeless",
            questionValue: "No",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "radio",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "YesNo_YN",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "f_stu_citizenship_status",
            condDsblParVal: ";BLANK;3;",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "f_stu_citizenship_status",
            condReqParVal: ";1;2;4;",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent:
              '<b>Homeless Status</b> - Answer "Yes" if the student is an unaccompanied youth who is or was homeless or at risk of being homeless.  <br /> <br />  -- "Homeless" means the student lacks fixed, regular and adequate housing (living in shelters, parks, motels or cars or living temporarily with other people because the student has nowhere else to go) <br />  -- "Unaccompanied" means the student is not living in the physical custody of the student\\\'s parents <br />  -- "Youth" means that the student is 23 years of age or younger or the student is still enrolled in high school as of the date of the application <br /> <br /> Financial aid administrators may require documentation of this status. <br />',
            helpStyle: "Expandable Hint",
            helpTitle: "Tell me more",
            textContent: "Mary is homeless, or at risk of becoming homeless.",
          },
        ],
      },
      {
        PageName: "StudentInfo5",
        PageHeading: "Is Mary homeless, or at risk of becoming homeless",
        FWFormItemList: [
          {
            itemType: "question",
            id: "StuHomelessHS",
            questionValue: "",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "select",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "YesNo_YN",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent:
              "Mary's high school or school district homeless liaison",
          },
          {
            itemType: "question",
            id: "StuHomelessShelter",
            questionValue: "",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "select",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "YesNo_YN",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent:
              "The director of an emergency shelter or transitional housing program funded by the U.S. Department of Housing and Urban Development",
          },
          {
            itemType: "question",
            id: "StuHomelessRunaway",
            questionValue: "",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "select",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "YesNo_YN",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent:
              "The director of a runaway or homeless youth basic center or transitional living program",
          },
        ],
      },
      {
        PageName: "StudentInfo7",
        PageHeading: "Student's Citizenship",
        FWFormItemList: [
          {
            itemType: "question",
            id: "f_stu_citizenship_status",
            questionValue: "Mexico",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "radio",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "CitizenshipStatus",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent:
              '<b>Citizenship </b> - Select "U.S. eligible noncitizen" if the student: <br /> <br /> -is a U.S. permanent resident (I-551) <br /> -is a conditional permanent resident (I-551C) <br /> -holds an I-94 with a designation of "Refugee," "Asylum Granted," "Parolee," T-Visa holder, or "Cuban-Haitian Entrant," "Victim of human trafficking," or <br /> -is a citizen of the Republic of Palau, the Republic of the Marshall Islands, or the Federated States of Micronesia. <br /> <br /> Select "other" if the student is not a citizen or permanent resident of the U.S. or Canada and does not meet the above criteria, including having been granted Deferred Action for Childhood Arrivals (DACA) status or having an F1, F2, J1, J2, or G series visa. <br /> <br /> If the student has dual citizenship, select U.S. citizen.',
            helpStyle: "Expandable Hint",
            helpTitle: "What if I'm not sure?",
            textContent: "Citizenship status",
          },
          {
            itemType: "question",
            id: "f_stu_perm_country",
            questionValue: "US",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "select",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "Country",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent:
              "<b>Country Where Student Lives</b> - Select the country where you live. This should be your permanent address, and may be different from where you receive your mail.<br />",
            helpStyle: "Notice",
            helpTitle: "",
            textContent: "Country where Mary lives",
          },
        ],
      },
      {
        PageName: "StudentInfo9",
        PageHeading: "What is Mary's permanent address",
        FWFormItemList: [
          {
            itemType: "question",
            id: "f_stu_perm_addr1",
            questionValue: "5 Main St",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "text",
            size: "40",
            minLength: "",
            maxLength: "40",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent: "Street address",
          },
          {
            itemType: "question",
            id: "f_stu_perm_addr2",
            questionValue: "",
            required: "N",
            requiredErrMsg: "",
            fieldType: "text",
            size: "40",
            minLength: "",
            maxLength: "40",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent: "Street address (line 2)",
          },
          {
            itemType: "question",
            id: "f_stu_perm_city",
            questionValue: "Westford",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "text",
            size: "40",
            minLength: "",
            maxLength: "30",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent: "City",
          },
          {
            itemType: "question",
            id: "f_stu_perm_country2",
            questionValue: "US",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "select",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "Country",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "Y",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "Y",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent: "Country",
          },
          {
            itemType: "question",
            id: "f_stu_perm_st",
            questionValue: "MA",
            required: "N",
            requiredErrMsg: "",
            fieldType: "select",
            size: "",
            minLength: "",
            maxLength: "",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "State",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "f_stu_perm_country2",
            condDsblParVal: "",
            condDsblParValNOTEQ: ";US;CA;",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "f_stu_perm_country2",
            condReqParVal: ";US;CA;",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent: "State/Province",
          },
          {
            itemType: "question",
            id: "f_stu_perm_zip",
            questionValue: "01886",
            required: "Y",
            requiredErrMsg: "",
            fieldType: "text",
            size: "20",
            minLength: "",
            maxLength: "17",
            format: "zip",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "f_stu_perm_country2",
            condReqParVal: ";US;CA;",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent: "",
            helpStyle: "",
            helpTitle: "",
            textContent: "ZIP/Postal Code",
          },
        ],
      },
      {
        PageName: "StudentInfo11",
        PageHeading: "What is Mary's CBFinAid Id?",
        FWFormItemList: [
          {
            itemType: "question",
            id: "CBFinAidIDEntered",
            questionValue: "12345678",
            required: "N",
            requiredErrMsg: "",
            fieldType: "text",
            size: "7",
            minLength: "7",
            maxLength: "7",
            format: "",
            displayFormat: "",
            rangeHigh: "",
            rangeLow: "",
            dict: "",
            checkboxCheckedValue: "",
            questionMsg: "",
            condDsblChild: "",
            condDsblParFld: "",
            condDsblParVal: "",
            condDsblParValNOTEQ: "",
            condDisableQuestion: "N",
            condReqChild: "",
            condReqParFld: "",
            condReqParVal: "",
            condReqOperator: "",
            condReqParValNOTEQ: "",
            validateFldCompare: "",
            validateOperator: "",
            validateVal: "",
            validateRGEX: "",
            validateErrMsg: "",
            helpContent:
              "<b>CBFinAidID</b> - If you have the student\\'s CBFinAId ID from last year or received one in an email, enter it here.  It will help us match this application to other data for this student.<br /> <br />If you don\\'t have a CBFinAid ID leave this question blank.<br />",
            helpStyle: "",
            helpTitle: "",
            textContent: "CBFinAidID",
          },
        ],
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
];
