 export const multiple = {
  "ReturnMessage": "",
  "Username": "<EMAIL>",
  "UserId": "F6r8XSIUYh8KsS5d@rzlVNaI6E4VA5@fYx/Cf03y8@JKiBjsOb7USQ==",
  "AwardYear": "2024",
  "ApplicationId": "yRXW7xv0uZcNy1SmgP6EyVKq1KYAQ0USCrFBeB53kG7Qq9cl@aaS1A==",
  "OrgFormId": "/QxfOQuJHKNSBvgA@mpXI04A5uN1zirecb0SBo97xhB8gmIpZYCs8A==",
  "FormId": "CB000000664",
  "MenuName": "ParentAssets",
  "PageName": "ParentAssetInfo7",
  "Multiple": "Y",
  "MultipleParentName": "ParRELocationList",
  "MultipleItemCount": "2",
  "MaxMultipleItems": "4",
  "PageHeading": "Parent Other Real Estate Details",
  "SectionHeading": "Parent Other Real Estate Details",
  "CBFinAidID": "ZAAAA1H",
  "FWFormItemList": {
    "ItemNumber": "0",
    "FWFormItemList": []
  },
  "MultipleItemList": {
    "ItemList": [
      {
        "ItemNumber": "1",
        "FWFormItemList": [
          {
            "itemType": "text",
            "id": "Text0",
            "questionValue": "",
            "required": "",
            "requiredErrMsg": "",
            "fieldType": "",
            "size": "",
            "minLength": "",
            "maxLength": "",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Provide details for each property."
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddressCountry",
            "questionValue": "US",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "select",
            "size": "",
            "minLength": "",
            "maxLength": "",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "Country",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Country"
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddress",
            "questionValue": "1 Vaca St",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "40",
            "minLength": "",
            "maxLength": "50",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Street address of this property"
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddress2",
            "questionValue": "",
            "required": "N",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "40",
            "minLength": "",
            "maxLength": "40",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Street address (line 2)"
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddressCity",
            "questionValue": "Naples",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "40",
            "minLength": "",
            "maxLength": "30",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "City"
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddressState",
            "questionValue": "FL",
            "required": "N",
            "requiredErrMsg": "",
            "fieldType": "select",
            "size": "",
            "minLength": "",
            "maxLength": "",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "State",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "State/Province"
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddressZip",
            "questionValue": "34120",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "20",
            "minLength": "",
            "maxLength": "17",
            "format": "zip",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "ZIP/Postal Code"
          },
          {
            "itemType": "question",
            "id": "ParOtherREMonths",
            "questionValue": "4",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "4",
            "minLength": "1",
            "maxLength": "2",
            "format": "number",
            "displayFormat": "",
            "rangeHigh": "12",
            "rangeLow": "0",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Number of months per year this property is rented to others"
          },
          {
            "itemType": "question",
            "id": "q_par_real_estate_purchase_yr",
            "questionValue": "2010",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "4",
            "minLength": "4",
            "maxLength": "4",
            "format": "number",
            "displayFormat": "",
            "rangeHigh": "CURRENTYEAR",
            "rangeLow": "1920",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "Enter the year the property was acquired, even if it was not directly purchased.",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Year the property was purchased"
          },
          {
            "itemType": "question",
            "id": "q_par_real_estate_purchase_pr",
            "questionValue": "500000",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "16",
            "minLength": "1",
            "maxLength": "10",
            "format": "money",
            "displayFormat": "",
            "rangeHigh": "9999999999999999",
            "rangeLow": "0",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "Enter the purchase price or market value at the time the property was acquired.  If the property was acquired with others, enter only your parents' portion of the value.",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Purchase price of this property"
          },
          {
            "itemType": "question",
            "id": "q_par_real_estate_val",
            "questionValue": "900000",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "16",
            "minLength": "1",
            "maxLength": "10",
            "format": "money",
            "displayFormat": "",
            "rangeHigh": "9999999999999999",
            "rangeLow": "0",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "The market value is the price the property could be sold for today.  Do not use assessed, insured, or tax value.  \r\n\r\nReport only the portion of the total value that is owned by the student's parents.",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Current market value of this property"
          },
          {
            "itemType": "question",
            "id": "q_par_real_estate_debt",
            "questionValue": "0",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "16",
            "minLength": "1",
            "maxLength": "10",
            "format": "money",
            "displayFormat": "",
            "rangeHigh": "9999999999999999",
            "rangeLow": "0",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "Include the current payoff amount of the mortgage and related debts.\r\n\r\nDo not include personal loans, consumer loans, interest due, escrow payments, insurance premiums, association fees, property taxes, or any other debts.\r\n\r\nReport only the portion of the total debt that is owed by the student's parents.",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Amount owed on this property"
          }
        ]
      },
      {
        "ItemNumber": "2",
        "FWFormItemList": [
          {
            "itemType": "text",
            "id": "Text0",
            "questionValue": "",
            "required": "",
            "requiredErrMsg": "",
            "fieldType": "",
            "size": "",
            "minLength": "",
            "maxLength": "",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Provide details for each property."
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddressCountry",
            "questionValue": "US",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "select",
            "size": "",
            "minLength": "",
            "maxLength": "",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "Country",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Country"
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddress",
            "questionValue": "9 Sunny Ln",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "40",
            "minLength": "",
            "maxLength": "50",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Street address of this property"
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddress2",
            "questionValue": "",
            "required": "N",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "40",
            "minLength": "",
            "maxLength": "40",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Street address (line 2)"
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddressCity",
            "questionValue": "Naples",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "40",
            "minLength": "",
            "maxLength": "30",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "City"
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddressState",
            "questionValue": "FL",
            "required": "N",
            "requiredErrMsg": "",
            "fieldType": "select",
            "size": "",
            "minLength": "",
            "maxLength": "",
            "format": "",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "State",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "State/Province"
          },
          {
            "itemType": "question",
            "id": "ParOtherREAddressZip",
            "questionValue": "34120",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "20",
            "minLength": "",
            "maxLength": "17",
            "format": "zip",
            "displayFormat": "",
            "rangeHigh": "",
            "rangeLow": "",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "ZIP/Postal Code"
          },
          {
            "itemType": "question",
            "id": "ParOtherREMonths",
            "questionValue": "9",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "4",
            "minLength": "1",
            "maxLength": "2",
            "format": "number",
            "displayFormat": "",
            "rangeHigh": "12",
            "rangeLow": "0",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Number of months per year this property is rented to others"
          },
          {
            "itemType": "question",
            "id": "q_par_real_estate_purchase_yr",
            "questionValue": "2011",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "4",
            "minLength": "4",
            "maxLength": "4",
            "format": "number",
            "displayFormat": "",
            "rangeHigh": "CURRENTYEAR",
            "rangeLow": "1920",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "Enter the year the property was acquired, even if it was not directly purchased.",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Year the property was purchased"
          },
          {
            "itemType": "question",
            "id": "q_par_real_estate_purchase_pr",
            "questionValue": "800000",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "16",
            "minLength": "1",
            "maxLength": "10",
            "format": "money",
            "displayFormat": "",
            "rangeHigh": "9999999999999999",
            "rangeLow": "0",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "Enter the purchase price or market value at the time the property was acquired.  If the property was acquired with others, enter only your parents' portion of the value.",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Purchase price of this property"
          },
          {
            "itemType": "question",
            "id": "q_par_real_estate_val",
            "questionValue": "1000000",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "16",
            "minLength": "1",
            "maxLength": "10",
            "format": "money",
            "displayFormat": "",
            "rangeHigh": "9999999999999999",
            "rangeLow": "0",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "The market value is the price the property could be sold for today.  Do not use assessed, insured, or tax value.  \r\n\r\nReport only the portion of the total value that is owned by the student's parents.",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Current market value of this property"
          },
          {
            "itemType": "question",
            "id": "q_par_real_estate_debt",
            "questionValue": "0",
            "required": "Y",
            "requiredErrMsg": "",
            "fieldType": "text",
            "size": "16",
            "minLength": "1",
            "maxLength": "10",
            "format": "money",
            "displayFormat": "",
            "rangeHigh": "9999999999999999",
            "rangeLow": "0",
            "dict": "",
            "checkboxCheckedValue": "",
            "questionMsg": "",
            "helpContent": "Include the current payoff amount of the mortgage and related debts.\r\n\r\nDo not include personal loans, consumer loans, interest due, escrow payments, insurance premiums, association fees, property taxes, or any other debts.\r\n\r\nReport only the portion of the total debt that is owed by the student's parents.",
            "helpStyle": "",
            "helpTitle": "",
            "textContent": "Amount owed on this property"
          }
        ]
      }
    ]
  },
  "FWDictsList": {
    "Dicts": [
      {
        "Display": "Country",
        "Name": "Country",
        "DictItems": [
          {
            "Value": "AF",
            "Display": "Afghanistan",
            "Order": "002"
          },
          {
            "Value": "AX",
            "Display": "Åland Islands",
            "Order": "003"
          },
          {
            "Value": "AL",
            "Display": "Albania",
            "Order": "004"
          },
          {
            "Value": "DZ",
            "Display": "Algeria",
            "Order": "005"
          },
          {
            "Value": "AD",
            "Display": "Andorra",
            "Order": "006"
          },
          {
            "Value": "AO",
            "Display": "Angola",
            "Order": "007"
          },
          {
            "Value": "AI",
            "Display": "Anguilla",
            "Order": "008"
          },
          {
            "Value": "AG",
            "Display": "Antigua and Barbuda",
            "Order": "009"
          },
          {
            "Value": "AR",
            "Display": "Argentina",
            "Order": "010"
          },
          {
            "Value": "AM",
            "Display": "Armenia",
            "Order": "011"
          },
          {
            "Value": "AW",
            "Display": "Aruba",
            "Order": "012"
          },
          {
            "Value": "AU",
            "Display": "Australia",
            "Order": "013"
          },
          {
            "Value": "AT",
            "Display": "Austria",
            "Order": "014"
          },
          {
            "Value": "AZ",
            "Display": "Azerbaijan",
            "Order": "015"
          },
          {
            "Value": "BS",
            "Display": "Bahamas",
            "Order": "016"
          },
          {
            "Value": "BH",
            "Display": "Bahrain",
            "Order": "017"
          },
          {
            "Value": "BD",
            "Display": "Bangladesh",
            "Order": "018"
          },
          {
            "Value": "BB",
            "Display": "Barbados",
            "Order": "019"
          },
          {
            "Value": "BY",
            "Display": "Belarus",
            "Order": "020"
          },
          {
            "Value": "BE",
            "Display": "Belgium",
            "Order": "021"
          },
          {
            "Value": "BZ",
            "Display": "Belize",
            "Order": "022"
          },
          {
            "Value": "BJ",
            "Display": "Benin",
            "Order": "023"
          },
          {
            "Value": "BM",
            "Display": "Bermuda",
            "Order": "024"
          },
          {
            "Value": "BT",
            "Display": "Bhutan",
            "Order": "025"
          },
          {
            "Value": "BO",
            "Display": "Bolivia (Plurinational State of)",
            "Order": "026"
          },
          {
            "Value": "BQ",
            "Display": "Bonaire, Sint Eustatius and Saba",
            "Order": "027"
          },
          {
            "Value": "BA",
            "Display": "Bosnia and Herzegovina",
            "Order": "028"
          },
          {
            "Value": "BW",
            "Display": "Botswana",
            "Order": "029"
          },
          {
            "Value": "BR",
            "Display": "Brazil",
            "Order": "030"
          },
          {
            "Value": "BN",
            "Display": "Brunei Darussalam",
            "Order": "031"
          },
          {
            "Value": "BG",
            "Display": "Bulgaria",
            "Order": "032"
          },
          {
            "Value": "BF",
            "Display": "Burkina Faso",
            "Order": "033"
          },
          {
            "Value": "BI",
            "Display": "Burundi",
            "Order": "034"
          },
          {
            "Value": "CV",
            "Display": "Cabo Verde",
            "Order": "035"
          },
          {
            "Value": "KH",
            "Display": "Cambodia",
            "Order": "036"
          },
          {
            "Value": "CM",
            "Display": "Cameroon",
            "Order": "037"
          },
          {
            "Value": "CA",
            "Display": "Canada",
            "Order": "038"
          },
          {
            "Value": "KY",
            "Display": "Cayman Islands",
            "Order": "039"
          },
          {
            "Value": "CF",
            "Display": "Central African Republic",
            "Order": "040"
          },
          {
            "Value": "TD",
            "Display": "Chad",
            "Order": "041"
          },
          {
            "Value": "CL",
            "Display": "Chile",
            "Order": "042"
          },
          {
            "Value": "CN",
            "Display": "China",
            "Order": "043"
          },
          {
            "Value": "CO",
            "Display": "Colombia",
            "Order": "044"
          },
          {
            "Value": "KM",
            "Display": "Comoros",
            "Order": "045"
          },
          {
            "Value": "CG",
            "Display": "Congo",
            "Order": "046"
          },
          {
            "Value": "CD",
            "Display": "Congo (the Democratic Republic of the)",
            "Order": "047"
          },
          {
            "Value": "CK",
            "Display": "Cook Islands",
            "Order": "048"
          },
          {
            "Value": "CR",
            "Display": "Costa Rica",
            "Order": "049"
          },
          {
            "Value": "CI",
            "Display": "Côte d'Ivoire",
            "Order": "050"
          },
          {
            "Value": "HR",
            "Display": "Croatia",
            "Order": "051"
          },
          {
            "Value": "CU",
            "Display": "Cuba",
            "Order": "052"
          },
          {
            "Value": "CW",
            "Display": "Curaçao",
            "Order": "053"
          },
          {
            "Value": "CY",
            "Display": "Cyprus",
            "Order": "054"
          },
          {
            "Value": "CZ",
            "Display": "Czechia",
            "Order": "055"
          },
          {
            "Value": "DK",
            "Display": "Denmark",
            "Order": "056"
          },
          {
            "Value": "DJ",
            "Display": "Djibouti",
            "Order": "057"
          },
          {
            "Value": "DM",
            "Display": "Dominica",
            "Order": "058"
          },
          {
            "Value": "DO",
            "Display": "Dominican Republic",
            "Order": "059"
          },
          {
            "Value": "EC",
            "Display": "Ecuador",
            "Order": "060"
          },
          {
            "Value": "EG",
            "Display": "Egypt",
            "Order": "061"
          },
          {
            "Value": "SV",
            "Display": "El Salvador",
            "Order": "062"
          },
          {
            "Value": "GQ",
            "Display": "Equatorial Guinea",
            "Order": "063"
          },
          {
            "Value": "ER",
            "Display": "Eritrea",
            "Order": "064"
          },
          {
            "Value": "EE",
            "Display": "Estonia",
            "Order": "065"
          },
          {
            "Value": "SZ",
            "Display": "Eswatini",
            "Order": "066"
          },
          {
            "Value": "ET",
            "Display": "Ethiopia",
            "Order": "067"
          },
          {
            "Value": "FK",
            "Display": "Falkland Islands [Malvinas]",
            "Order": "068"
          },
          {
            "Value": "FO",
            "Display": "Faroe Islands",
            "Order": "069"
          },
          {
            "Value": "FJ",
            "Display": "Fiji",
            "Order": "070"
          },
          {
            "Value": "FI",
            "Display": "Finland",
            "Order": "071"
          },
          {
            "Value": "FR",
            "Display": "France",
            "Order": "072"
          },
          {
            "Value": "GF",
            "Display": "French Guiana",
            "Order": "073"
          },
          {
            "Value": "PF",
            "Display": "French Polynesia",
            "Order": "074"
          },
          {
            "Value": "GA",
            "Display": "Gabon",
            "Order": "075"
          },
          {
            "Value": "GM",
            "Display": "Gambia",
            "Order": "076"
          },
          {
            "Value": "GZ",
            "Display": "Gaza Strip",
            "Order": "077"
          },
          {
            "Value": "GE",
            "Display": "Georgia",
            "Order": "078"
          },
          {
            "Value": "DE",
            "Display": "Germany",
            "Order": "079"
          },
          {
            "Value": "GH",
            "Display": "Ghana",
            "Order": "080"
          },
          {
            "Value": "GI",
            "Display": "Gibraltar",
            "Order": "081"
          },
          {
            "Value": "GR",
            "Display": "Greece",
            "Order": "082"
          },
          {
            "Value": "GL",
            "Display": "Greenland",
            "Order": "083"
          },
          {
            "Value": "GD",
            "Display": "Grenada",
            "Order": "084"
          },
          {
            "Value": "GP",
            "Display": "Guadeloupe",
            "Order": "085"
          },
          {
            "Value": "GT",
            "Display": "Guatemala",
            "Order": "086"
          },
          {
            "Value": "GG",
            "Display": "Guernsey",
            "Order": "087"
          },
          {
            "Value": "GN",
            "Display": "Guinea",
            "Order": "088"
          },
          {
            "Value": "GW",
            "Display": "Guinea-Bissau",
            "Order": "089"
          },
          {
            "Value": "GY",
            "Display": "Guyana",
            "Order": "090"
          },
          {
            "Value": "HT",
            "Display": "Haiti",
            "Order": "091"
          },
          {
            "Value": "VA",
            "Display": "Holy See [Vatican City State]",
            "Order": "092"
          },
          {
            "Value": "HN",
            "Display": "Honduras",
            "Order": "093"
          },
          {
            "Value": "HK",
            "Display": "Hong Kong",
            "Order": "094"
          },
          {
            "Value": "HU",
            "Display": "Hungary",
            "Order": "095"
          },
          {
            "Value": "IS",
            "Display": "Iceland",
            "Order": "096"
          },
          {
            "Value": "IN",
            "Display": "India",
            "Order": "097"
          },
          {
            "Value": "ID",
            "Display": "Indonesia",
            "Order": "098"
          },
          {
            "Value": "IR",
            "Display": "Iran (Islamic Republic of)",
            "Order": "099"
          },
          {
            "Value": "IQ",
            "Display": "Iraq",
            "Order": "100"
          },
          {
            "Value": "IE",
            "Display": "Ireland",
            "Order": "101"
          },
          {
            "Value": "IM",
            "Display": "Isle of Man",
            "Order": "102"
          },
          {
            "Value": "IL",
            "Display": "Israel",
            "Order": "103"
          },
          {
            "Value": "IT",
            "Display": "Italy",
            "Order": "104"
          },
          {
            "Value": "JM",
            "Display": "Jamaica",
            "Order": "105"
          },
          {
            "Value": "JP",
            "Display": "Japan",
            "Order": "106"
          },
          {
            "Value": "JE",
            "Display": "Jersey",
            "Order": "107"
          },
          {
            "Value": "JO",
            "Display": "Jordan",
            "Order": "108"
          },
          {
            "Value": "KZ",
            "Display": "Kazakhstan",
            "Order": "109"
          },
          {
            "Value": "KE",
            "Display": "Kenya",
            "Order": "110"
          },
          {
            "Value": "KI",
            "Display": "Kiribati",
            "Order": "111"
          },
          {
            "Value": "KP",
            "Display": "Korea (the Democratic People's Republic of)",
            "Order": "112"
          },
          {
            "Value": "KR",
            "Display": "Korea (the Republic of)",
            "Order": "113"
          },
          {
            "Value": "KS",
            "Display": "Kosovo",
            "Order": "114"
          },
          {
            "Value": "KW",
            "Display": "Kuwait",
            "Order": "115"
          },
          {
            "Value": "KG",
            "Display": "Kyrgyzstan",
            "Order": "116"
          },
          {
            "Value": "LA",
            "Display": "Lao People's Democratic Republic (the)",
            "Order": "117"
          },
          {
            "Value": "LV",
            "Display": "Latvia",
            "Order": "118"
          },
          {
            "Value": "LB",
            "Display": "Lebanon",
            "Order": "119"
          },
          {
            "Value": "LS",
            "Display": "Lesotho",
            "Order": "120"
          },
          {
            "Value": "LR",
            "Display": "Liberia",
            "Order": "121"
          },
          {
            "Value": "LY",
            "Display": "Libya",
            "Order": "122"
          },
          {
            "Value": "LI",
            "Display": "Liechtenstein",
            "Order": "123"
          },
          {
            "Value": "LT",
            "Display": "Lithuania",
            "Order": "124"
          },
          {
            "Value": "LU",
            "Display": "Luxembourg",
            "Order": "125"
          },
          {
            "Value": "MO",
            "Display": "Macao",
            "Order": "126"
          },
          {
            "Value": "MG",
            "Display": "Madagascar",
            "Order": "127"
          },
          {
            "Value": "MW",
            "Display": "Malawi",
            "Order": "128"
          },
          {
            "Value": "MY",
            "Display": "Malaysia",
            "Order": "129"
          },
          {
            "Value": "MV",
            "Display": "Maldives",
            "Order": "130"
          },
          {
            "Value": "ML",
            "Display": "Mali",
            "Order": "131"
          },
          {
            "Value": "MT",
            "Display": "Malta",
            "Order": "132"
          },
          {
            "Value": "MQ",
            "Display": "Martinique",
            "Order": "133"
          },
          {
            "Value": "MR",
            "Display": "Mauritania",
            "Order": "134"
          },
          {
            "Value": "MU",
            "Display": "Mauritius",
            "Order": "135"
          },
          {
            "Value": "YT",
            "Display": "Mayotte",
            "Order": "136"
          },
          {
            "Value": "MX",
            "Display": "Mexico",
            "Order": "137"
          },
          {
            "Value": "MD",
            "Display": "Moldova (the Republic of)",
            "Order": "138"
          },
          {
            "Value": "MC",
            "Display": "Monaco",
            "Order": "139"
          },
          {
            "Value": "MN",
            "Display": "Mongolia",
            "Order": "140"
          },
          {
            "Value": "ME",
            "Display": "Montenegro",
            "Order": "141"
          },
          {
            "Value": "MS",
            "Display": "Montserrat",
            "Order": "142"
          },
          {
            "Value": "MA",
            "Display": "Morocco",
            "Order": "143"
          },
          {
            "Value": "MZ",
            "Display": "Mozambique",
            "Order": "144"
          },
          {
            "Value": "MM",
            "Display": "Myanmar",
            "Order": "145"
          },
          {
            "Value": "NA",
            "Display": "Namibia",
            "Order": "146"
          },
          {
            "Value": "NR",
            "Display": "Nauru",
            "Order": "147"
          },
          {
            "Value": "NP",
            "Display": "Nepal",
            "Order": "148"
          },
          {
            "Value": "NL",
            "Display": "Netherlands",
            "Order": "149"
          },
          {
            "Value": "NC",
            "Display": "New Caledonia",
            "Order": "150"
          },
          {
            "Value": "NZ",
            "Display": "New Zealand",
            "Order": "151"
          },
          {
            "Value": "NI",
            "Display": "Nicaragua",
            "Order": "152"
          },
          {
            "Value": "NE",
            "Display": "Niger",
            "Order": "153"
          },
          {
            "Value": "NG",
            "Display": "Nigeria",
            "Order": "154"
          },
          {
            "Value": "NU",
            "Display": "Niue",
            "Order": "155"
          },
          {
            "Value": "MK",
            "Display": "North Macedonia",
            "Order": "156"
          },
          {
            "Value": "NO",
            "Display": "Norway",
            "Order": "157"
          },
          {
            "Value": "OM",
            "Display": "Oman",
            "Order": "158"
          },
          {
            "Value": "PK",
            "Display": "Pakistan",
            "Order": "159"
          },
          {
            "Value": "PS",
            "Display": "Palestine, State of",
            "Order": "160"
          },
          {
            "Value": "PA",
            "Display": "Panama",
            "Order": "161"
          },
          {
            "Value": "PG",
            "Display": "Papua New Guinea",
            "Order": "162"
          },
          {
            "Value": "PY",
            "Display": "Paraguay",
            "Order": "163"
          },
          {
            "Value": "PE",
            "Display": "Peru",
            "Order": "164"
          },
          {
            "Value": "PH",
            "Display": "Philippines",
            "Order": "165"
          },
          {
            "Value": "PL",
            "Display": "Poland",
            "Order": "166"
          },
          {
            "Value": "PT",
            "Display": "Portugal",
            "Order": "167"
          },
          {
            "Value": "QA",
            "Display": "Qatar",
            "Order": "168"
          },
          {
            "Value": "RE",
            "Display": "Réunion",
            "Order": "169"
          },
          {
            "Value": "RO",
            "Display": "Romania",
            "Order": "170"
          },
          {
            "Value": "RU",
            "Display": "Russian Federation",
            "Order": "171"
          },
          {
            "Value": "RW",
            "Display": "Rwanda",
            "Order": "172"
          },
          {
            "Value": "BL",
            "Display": "Saint Barthélemy",
            "Order": "173"
          },
          {
            "Value": "SH",
            "Display": "Saint Helena, Ascension and Tristan da Cunha",
            "Order": "174"
          },
          {
            "Value": "KN",
            "Display": "Saint Kitts and Nevis",
            "Order": "175"
          },
          {
            "Value": "LC",
            "Display": "Saint Lucia",
            "Order": "176"
          },
          {
            "Value": "MF",
            "Display": "Saint Martin (French part)",
            "Order": "177"
          },
          {
            "Value": "PM",
            "Display": "Saint Pierre and Miquelon",
            "Order": "178"
          },
          {
            "Value": "VC",
            "Display": "Saint Vincent and the Grenadines",
            "Order": "179"
          },
          {
            "Value": "WS",
            "Display": "Samoa",
            "Order": "180"
          },
          {
            "Value": "SM",
            "Display": "San Marino",
            "Order": "181"
          },
          {
            "Value": "ST",
            "Display": "Sao Tome and Principe",
            "Order": "182"
          },
          {
            "Value": "SA",
            "Display": "Saudi Arabia",
            "Order": "183"
          },
          {
            "Value": "SN",
            "Display": "Senegal",
            "Order": "184"
          },
          {
            "Value": "RS",
            "Display": "Serbia",
            "Order": "185"
          },
          {
            "Value": "SC",
            "Display": "Seychelles",
            "Order": "186"
          },
          {
            "Value": "SL",
            "Display": "Sierra Leone",
            "Order": "187"
          },
          {
            "Value": "SG",
            "Display": "Singapore",
            "Order": "188"
          },
          {
            "Value": "SX",
            "Display": "Sint Maarten (Dutch part)",
            "Order": "189"
          },
          {
            "Value": "SK",
            "Display": "Slovakia",
            "Order": "190"
          },
          {
            "Value": "SI",
            "Display": "Slovenia",
            "Order": "191"
          },
          {
            "Value": "SB",
            "Display": "Solomon Islands",
            "Order": "192"
          },
          {
            "Value": "SO",
            "Display": "Somalia",
            "Order": "193"
          },
          {
            "Value": "ZA",
            "Display": "South Africa",
            "Order": "194"
          },
          {
            "Value": "SS",
            "Display": "South Sudan",
            "Order": "195"
          },
          {
            "Value": "ES",
            "Display": "Spain",
            "Order": "196"
          },
          {
            "Value": "LK",
            "Display": "Sri Lanka",
            "Order": "197"
          },
          {
            "Value": "SD",
            "Display": "Sudan",
            "Order": "198"
          },
          {
            "Value": "SR",
            "Display": "Suriname",
            "Order": "199"
          },
          {
            "Value": "SE",
            "Display": "Sweden",
            "Order": "200"
          },
          {
            "Value": "CH",
            "Display": "Switzerland",
            "Order": "201"
          },
          {
            "Value": "SY",
            "Display": "Syrian Arab Republic",
            "Order": "202"
          },
          {
            "Value": "TW",
            "Display": "Taiwan (Province of China)",
            "Order": "203"
          },
          {
            "Value": "TJ",
            "Display": "Tajikistan",
            "Order": "204"
          },
          {
            "Value": "TZ",
            "Display": "Tanzania, United Republic of",
            "Order": "205"
          },
          {
            "Value": "TH",
            "Display": "Thailand",
            "Order": "206"
          },
          {
            "Value": "TL",
            "Display": "Timor-Leste",
            "Order": "207"
          },
          {
            "Value": "TG",
            "Display": "Togo",
            "Order": "208"
          },
          {
            "Value": "TK",
            "Display": "Tokelau",
            "Order": "209"
          },
          {
            "Value": "TO",
            "Display": "Tonga",
            "Order": "210"
          },
          {
            "Value": "TT",
            "Display": "Trinidad and Tobago",
            "Order": "211"
          },
          {
            "Value": "TN",
            "Display": "Tunisia",
            "Order": "212"
          },
          {
            "Value": "TR",
            "Display": "Turkey",
            "Order": "213"
          },
          {
            "Value": "TM",
            "Display": "Turkmenistan",
            "Order": "214"
          },
          {
            "Value": "TC",
            "Display": "Turks and Caicos Islands",
            "Order": "215"
          },
          {
            "Value": "TV",
            "Display": "Tuvalu",
            "Order": "216"
          },
          {
            "Value": "UG",
            "Display": "Uganda",
            "Order": "217"
          },
          {
            "Value": "UA",
            "Display": "Ukraine",
            "Order": "218"
          },
          {
            "Value": "AE",
            "Display": "United Arab Emirates",
            "Order": "219"
          },
          {
            "Value": "GB",
            "Display": "United Kingdom ",
            "Order": "220"
          },
          {
            "Value": "US",
            "Display": "United States",
            "Order": "001"
          },
          {
            "Value": "UY",
            "Display": "Uruguay",
            "Order": "221"
          },
          {
            "Value": "UZ",
            "Display": "Uzbekistan",
            "Order": "222"
          },
          {
            "Value": "VU",
            "Display": "Vanuatu",
            "Order": "223"
          },
          {
            "Value": "VE",
            "Display": "Venezuela (Bolivarian Republic of)",
            "Order": "224"
          },
          {
            "Value": "VN",
            "Display": "Viet Nam",
            "Order": "225"
          },
          {
            "Value": "VG",
            "Display": "Virgin Islands (British)",
            "Order": "226"
          },
          {
            "Value": "WF",
            "Display": "Wallis and Futuna",
            "Order": "227"
          },
          {
            "Value": "WE",
            "Display": "West Bank",
            "Order": "228"
          },
          {
            "Value": "EH",
            "Display": "Western Sahara",
            "Order": "229"
          },
          {
            "Value": "YE",
            "Display": "Yemen",
            "Order": "230"
          },
          {
            "Value": "ZM",
            "Display": "Zambia",
            "Order": "231"
          },
          {
            "Value": "ZW",
            "Display": "Zimbabwe",
            "Order": "232"
          }
        ]
      },
      {
        "Display": "State",
        "Name": "State",
        "DictItems": [
          {
            "Value": "AL",
            "Display": "Alabama",
            "Order": "001"
          },
          {
            "Value": "AK",
            "Display": "Alaska",
            "Order": "002"
          },
          {
            "Value": "AB",
            "Display": "Alberta",
            "Order": "003"
          },
          {
            "Value": "AS",
            "Display": "American Samoa",
            "Order": "004"
          },
          {
            "Value": "AZ",
            "Display": "Arizona",
            "Order": "005"
          },
          {
            "Value": "AR",
            "Display": "Arkansas",
            "Order": "006"
          },
          {
            "Value": "AA",
            "Display": "Armed Forces Americas",
            "Order": "007"
          },
          {
            "Value": "AE",
            "Display": "Armed Forces Europe, Canada, M.E.",
            "Order": "008"
          },
          {
            "Value": "AP",
            "Display": "Armed Forces Pacific",
            "Order": "009"
          },
          {
            "Value": "BC",
            "Display": "British Columbia",
            "Order": "010"
          },
          {
            "Value": "CA",
            "Display": "California",
            "Order": "011"
          },
          {
            "Value": "CO",
            "Display": "Colorado",
            "Order": "013"
          },
          {
            "Value": "CT",
            "Display": "Connecticut",
            "Order": "014"
          },
          {
            "Value": "DE",
            "Display": "Delaware",
            "Order": "015"
          },
          {
            "Value": "DC",
            "Display": "District of Columbia",
            "Order": "016"
          },
          {
            "Value": "FM",
            "Display": "Federated States of Micronesia",
            "Order": "017"
          },
          {
            "Value": "FL",
            "Display": "Florida",
            "Order": "018"
          },
          {
            "Value": "GA",
            "Display": "Georgia",
            "Order": "020"
          },
          {
            "Value": "GU",
            "Display": "Guam",
            "Order": "021"
          },
          {
            "Value": "HI",
            "Display": "Hawaii",
            "Order": "022"
          },
          {
            "Value": "ID",
            "Display": "Idaho",
            "Order": "023"
          },
          {
            "Value": "IL",
            "Display": "Illinois",
            "Order": "024"
          },
          {
            "Value": "IN",
            "Display": "Indiana",
            "Order": "025"
          },
          {
            "Value": "IA",
            "Display": "Iowa",
            "Order": "026"
          },
          {
            "Value": "KS",
            "Display": "Kansas",
            "Order": "027"
          },
          {
            "Value": "KY",
            "Display": "Kentucky",
            "Order": "028"
          },
          {
            "Value": "LA",
            "Display": "Louisiana",
            "Order": "029"
          },
          {
            "Value": "ME",
            "Display": "Maine",
            "Order": "030"
          },
          {
            "Value": "MB",
            "Display": "Manitoba",
            "Order": "031"
          },
          {
            "Value": "MH",
            "Display": "Marshall Islands",
            "Order": "032"
          },
          {
            "Value": "MD",
            "Display": "Maryland",
            "Order": "033"
          },
          {
            "Value": "MA",
            "Display": "Massachusetts",
            "Order": "034"
          },
          {
            "Value": "MI",
            "Display": "Michigan",
            "Order": "036"
          },
          {
            "Value": "MN",
            "Display": "Minnesota",
            "Order": "037"
          },
          {
            "Value": "MS",
            "Display": "Mississippi",
            "Order": "038"
          },
          {
            "Value": "MO",
            "Display": "Missouri",
            "Order": "039"
          },
          {
            "Value": "MT",
            "Display": "Montana",
            "Order": "040"
          },
          {
            "Value": "NE",
            "Display": "Nebraska",
            "Order": "041"
          },
          {
            "Value": "NV",
            "Display": "Nevada",
            "Order": "042"
          },
          {
            "Value": "NB",
            "Display": "New Brunswick",
            "Order": "043"
          },
          {
            "Value": "NH",
            "Display": "New Hampshire",
            "Order": "044"
          },
          {
            "Value": "NJ",
            "Display": "New Jersey",
            "Order": "045"
          },
          {
            "Value": "NM",
            "Display": "New Mexico",
            "Order": "046"
          },
          {
            "Value": "NY",
            "Display": "New York",
            "Order": "047"
          },
          {
            "Value": "NF",
            "Display": "Newfoundland",
            "Order": "048"
          },
          {
            "Value": "NL",
            "Display": "Newfoundland and Labrador",
            "Order": "049"
          },
          {
            "Value": "NC",
            "Display": "North Carolina",
            "Order": "050"
          },
          {
            "Value": "ND",
            "Display": "North Dakota",
            "Order": "051"
          },
          {
            "Value": "MP",
            "Display": "Northern Mariana Islands",
            "Order": "052"
          },
          {
            "Value": "NT",
            "Display": "Northwest Territories",
            "Order": "053"
          },
          {
            "Value": "NS",
            "Display": "Nova Scotia",
            "Order": "054"
          },
          {
            "Value": "NU",
            "Display": "Nunavut",
            "Order": "055"
          },
          {
            "Value": "OH",
            "Display": "Ohio",
            "Order": "056"
          },
          {
            "Value": "OK",
            "Display": "Oklahoma",
            "Order": "057"
          },
          {
            "Value": "ON",
            "Display": "Ontario",
            "Order": "058"
          },
          {
            "Value": "OR",
            "Display": "Oregon",
            "Order": "059"
          },
          {
            "Value": "PW",
            "Display": "Palau",
            "Order": "060"
          },
          {
            "Value": "PA",
            "Display": "Pennsylvania",
            "Order": "061"
          },
          {
            "Value": "PE",
            "Display": "Prince Edward Island",
            "Order": "062"
          },
          {
            "Value": "PR",
            "Display": "Puerto Rico",
            "Order": "063"
          },
          {
            "Value": "QC",
            "Display": "Quebec",
            "Order": "064"
          },
          {
            "Value": "RI",
            "Display": "Rhode Island",
            "Order": "065"
          },
          {
            "Value": "SK",
            "Display": "Saskatchewan",
            "Order": "066"
          },
          {
            "Value": "SC",
            "Display": "South Carolina",
            "Order": "067"
          },
          {
            "Value": "SD",
            "Display": "South Dakota",
            "Order": "068"
          },
          {
            "Value": "TN",
            "Display": "Tennessee",
            "Order": "069"
          },
          {
            "Value": "TX",
            "Display": "Texas",
            "Order": "070"
          },
          {
            "Value": "UT",
            "Display": "Utah",
            "Order": "071"
          },
          {
            "Value": "VT",
            "Display": "Vermont",
            "Order": "072"
          },
          {
            "Value": "VI",
            "Display": "Virgin Islands",
            "Order": "073"
          },
          {
            "Value": "VA",
            "Display": "Virginia",
            "Order": "074"
          },
          {
            "Value": "WA",
            "Display": "Washington",
            "Order": "075"
          },
          {
            "Value": "WV",
            "Display": "West Virginia",
            "Order": "076"
          },
          {
            "Value": "WI",
            "Display": "Wisconsin",
            "Order": "077"
          },
          {
            "Value": "WY",
            "Display": "Wyoming",
            "Order": "078"
          },
          {
            "Value": "YT",
            "Display": "Yukon",
            "Order": "079"
          },
          {
            "Value": "FC",
            "Display": "None of the above",
            "Order": "080"
          }
        ]
      }
    ],
    "Count": 0
  }
}
//     {"ReturnMessage": "",
//     "Username": "<EMAIL>",
//     "UserId": "F6r8XSIUYh8KsS5d@rzlVNaI6E4VA5@fYx/Cf03y8@JKiBjsOb7USQ==",
//     "AwardYear": "2024",
//     "ApplicationId": "yRXW7xv0uZcNy1SmgP6EyVKq1KYAQ0USCrFBeB53kG7Qq9cl@aaS1A==",
//     "OrgFormId": "/QxfOQuJHKNSBvgA@mpXI04A5uN1zirecb0SBo97xhB8gmIpZYCs8A==",
//     "FormId": "CB000000664",
//     "MenuName": "ParentAssets",
//     "PageName": "ParentAssetInfo8",
//     "Multiple": "Y",
//     "MultipleParentName": "ParRELocationList",
//     "MultipleItemName": "ParREOther",
//     "MultipleItemCount": "2",
//     "PageHeading": "Parent Other Real Estate Details ",
//     "CBFinAidID": "ZAAAA1H",
//     "ItemList": [
//       {
//         "ItemNumber": "1",
//         "Action":"",
//         "SectionHeading": "Parent Other Real Estate Details 1 Main St",
//         "FWFormItemList": [
//           {
//             "itemType": "question",
//             "id": "ParOtherREMonths",
//             "questionValue": "4",
//             "required": "Y",
//             "requiredErrMsg": "",
//             "fieldType": "text",
//             "size": "4",
//             "minLength": "1",
//             "maxLength": "2",
//             "format": "number",
//             "displayFormat": "",
//             "rangeHigh": "12",
//             "rangeLow": "0",
//             "dict": "",
//             "checkboxCheckedValue": "",
//             "questionMsg": "",
//             "condDsblChild": "",
//             "condDsblParFld": "",
//             "condDsblParVal": "",
//             "condDsblParValNOTEQ": "",
//             "condDisableQuestion": "",
//             "condReqChild": "",
//             "condReqParFld": "",
//             "condReqParVal": "",
//             "condReqOperator": "",
//             "condReqParValNOTEQ": "",
//             "validateFldCompare": "",
//             "validateOperator": "",
//             "validateVal": "",
//             "validateRGEX": "",
//             "validateErrMsg": "",
//             "helpContent": "",
//             "helpStyle": "",
//             "helpTitle": "",
//             "textContent": "Number of months per year this property is rented to others"
//           },
//           {
//             "itemType": "question",
//             "id": "q_par_real_estate_purchase_yr",
//             "questionValue": "2010",
//             "required": "Y",
//             "requiredErrMsg": "",
//             "fieldType": "text",
//             "size": "4",
//             "minLength": "4",
//             "maxLength": "4",
//             "format": "number",
//             "displayFormat": "",
//             "rangeHigh": "CURRENTYEAR",
//             "rangeLow": "1920",
//             "dict": "",
//             "checkboxCheckedValue": "",
//             "questionMsg": "",
//             "condDsblChild": "",
//             "condDsblParFld": "",
//             "condDsblParVal": "",
//             "condDsblParValNOTEQ": "",
//             "condDisableQuestion": "",
//             "condReqChild": "",
//             "condReqParFld": "",
//             "condReqParVal": "",
//             "condReqOperator": "",
//             "condReqParValNOTEQ": "",
//             "validateFldCompare": "",
//             "validateOperator": "",
//             "validateVal": "",
//             "validateRGEX": "",
//             "validateErrMsg": "",
//             "helpContent": "Enter the year the property was acquired, even if it was not directly purchased.",
//             "helpStyle": "Notice",
//             "helpTitle": "",
//             "textContent": "Year the property was purchased"
//           },
//           {
//             "itemType": "question",
//             "id": "q_par_real_estate_purchase_pr",
//             "questionValue": "500000",
//             "required": "Y",
//             "requiredErrMsg": "",
//             "fieldType": "text",
//             "size": "16",
//             "minLength": "1",
//             "maxLength": "10",
//             "format": "money",
//             "displayFormat": "",
//             "rangeHigh": "9999999999999999",
//             "rangeLow": "0",
//             "dict": "",
//             "checkboxCheckedValue": "",
//             "questionMsg": "",
//             "condDsblChild": "",
//             "condDsblParFld": "",
//             "condDsblParVal": "",
//             "condDsblParValNOTEQ": "",
//             "condDisableQuestion": "",
//             "condReqChild": "",
//             "condReqParFld": "",
//             "condReqParVal": "",
//             "condReqOperator": "",
//             "condReqParValNOTEQ": "",
//             "validateFldCompare": "",
//             "validateOperator": "",
//             "validateVal": "",
//             "validateRGEX": "",
//             "validateErrMsg": "",
//             "helpContent": "Enter the purchase price or market value at the time the property was acquired.  If the property was acquired with others, enter only your parents' portion of the value.",
//             "helpStyle": "",
//             "helpTitle": "",
//             "textContent": "Purchase price of this property"
//           }
//         ]
//       },
//       {
//         "ItemNumber": "2",
//         "Action":"",
//         "SectionHeading": "Parent Other Real Estate Details 2 Main St",
//         "FWFormItemList": [
//           {
//             "itemType": "question",
//             "id": "ParOtherREMonths",
//             "questionValue": "4",
//             "required": "Y",
//             "requiredErrMsg": "",
//             "fieldType": "text",
//             "size": "4",
//             "minLength": "1",
//             "maxLength": "2",
//             "format": "number",
//             "displayFormat": "",
//             "rangeHigh": "12",
//             "rangeLow": "0",
//             "dict": "",
//             "checkboxCheckedValue": "",
//             "questionMsg": "",
//             "condDsblChild": "",
//             "condDsblParFld": "",
//             "condDsblParVal": "",
//             "condDsblParValNOTEQ": "",
//             "condDisableQuestion": "",
//             "condReqChild": "",
//             "condReqParFld": "",
//             "condReqParVal": "",
//             "condReqOperator": "",
//             "condReqParValNOTEQ": "",
//             "validateFldCompare": "",
//             "validateOperator": "",
//             "validateVal": "",
//             "validateRGEX": "",
//             "validateErrMsg": "",
//             "helpContent": "",
//             "helpStyle": "",
//             "helpTitle": "",
//             "textContent": "Number of months per year this property is rented to others"
//           },
//           {
//             "itemType": "question",
//             "id": "q_par_real_estate_purchase_yr",
//             "questionValue": "2010",
//             "required": "Y",
//             "requiredErrMsg": "",
//             "fieldType": "text",
//             "size": "4",
//             "minLength": "4",
//             "maxLength": "4",
//             "format": "number",
//             "displayFormat": "",
//             "rangeHigh": "CURRENTYEAR",
//             "rangeLow": "1920",
//             "dict": "",
//             "checkboxCheckedValue": "",
//             "questionMsg": "",
//             "condDsblChild": "",
//             "condDsblParFld": "",
//             "condDsblParVal": "",
//             "condDsblParValNOTEQ": "",
//             "condDisableQuestion": "",
//             "condReqChild": "",
//             "condReqParFld": "",
//             "condReqParVal": "",
//             "condReqOperator": "",
//             "condReqParValNOTEQ": "",
//             "validateFldCompare": "",
//             "validateOperator": "",
//             "validateVal": "",
//             "validateRGEX": "",
//             "validateErrMsg": "",
//             "helpContent": "Enter the year the property was acquired, even if it was not directly purchased.",
//             "helpStyle": "Notice",
//             "helpTitle": "",
//             "textContent": "Year the property was purchased"
//           },
//           {
//             "itemType": "question",
//             "id": "q_par_real_estate_purchase_pr",
//             "questionValue": "500000",
//             "required": "Y",
//             "requiredErrMsg": "",
//             "fieldType": "text",
//             "size": "16",
//             "minLength": "1",
//             "maxLength": "10",
//             "format": "money",
//             "displayFormat": "",
//             "rangeHigh": "9999999999999999",
//             "rangeLow": "0",
//             "dict": "",
//             "checkboxCheckedValue": "",
//             "questionMsg": "",
//             "condDsblChild": "",
//             "condDsblParFld": "",
//             "condDsblParVal": "",
//             "condDsblParValNOTEQ": "",
//             "condDisableQuestion": "",
//             "condReqChild": "",
//             "condReqParFld": "",
//             "condReqParVal": "",
//             "condReqOperator": "",
//             "condReqParValNOTEQ": "",
//             "validateFldCompare": "",
//             "validateOperator": "",
//             "validateVal": "",
//             "validateRGEX": "",
//             "validateErrMsg": "",
//             "helpContent": "Enter the purchase price or market value at the time the property was acquired.  If the property was acquired with others, enter only your parents' portion of the value.",
//             "helpStyle": "",
//             "helpTitle": "",
//             "textContent": "Purchase price of this property"
//           }
//         ]
//       }
//     ],
//     "FWDictsList": {
//       "Dicts": [],
//       "Count": 0
//     }
//   }