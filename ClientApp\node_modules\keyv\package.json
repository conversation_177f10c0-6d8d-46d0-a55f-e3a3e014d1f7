{"name": "keyv", "version": "4.5.0", "description": "Simple key-value storage with support for multiple backends", "main": "src/index.js", "scripts": {"test": "xo && nyc ava --serial", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "repository": {"type": "git", "url": "git+https://github.com/jaredwray/keyv.git"}, "keywords": ["key", "value", "store", "cache", "ttl"], "author": "<PERSON> <<EMAIL>> (http://jaredwray.com)", "license": "MIT", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "homepage": "https://github.com/jaredwray/keyv", "dependencies": {"json-buffer": "3.0.1"}, "devDependencies": {"@keyv/test-suite": "*", "ava": "^4.3.0", "eslint": "^8.19.0", "eslint-plugin-promise": "^6.0.0", "nyc": "^15.1.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "tsd": "^0.22.0", "typescript": "^4.7.4", "xo": "^0.51.0"}, "tsd": {"directory": "test"}, "types": "./src/index.d.ts", "files": ["src"]}