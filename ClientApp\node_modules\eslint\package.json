{"name": "eslint", "version": "7.32.0", "author": "<PERSON> <<EMAIL>>", "description": "An AST-based pattern checker for JavaScript.", "bin": {"eslint": "./bin/eslint.js"}, "main": "./lib/api.js", "scripts": {"test": "node Makefile.js test", "test:cli": "mocha", "lint": "node Makefile.js lint", "fix": "node Makefile.js lint -- fix", "fuzz": "node Makefile.js fuzz", "generate-release": "node Makefile.js generateRelease", "generate-alpharelease": "node Makefile.js generatePrerelease -- alpha", "generate-betarelease": "node Makefile.js generatePrerelease -- beta", "generate-rcrelease": "node Makefile.js generatePrerelease -- rc", "publish-release": "node Makefile.js publishRelease", "docs": "node Makefile.js docs", "gensite": "node Makefile.js gensite", "webpack": "node Makefile.js webpack", "perf": "node Makefile.js perf"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": "eslint --fix", "*.md": "markdownlint"}, "files": ["LICENSE", "README.md", "bin", "conf", "lib", "messages"], "repository": "eslint/eslint", "funding": "https://opencollective.com/eslint", "homepage": "https://eslint.org", "bugs": "https://github.com/eslint/eslint/issues/", "dependencies": {"@babel/code-frame": "7.12.11", "@eslint/eslintrc": "^0.4.3", "@humanwhocodes/config-array": "^0.5.0", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.0.1", "doctrine": "^3.0.0", "enquirer": "^2.3.5", "escape-string-regexp": "^4.0.0", "eslint-scope": "^5.1.1", "eslint-utils": "^2.1.0", "eslint-visitor-keys": "^2.0.0", "espree": "^7.3.1", "esquery": "^1.4.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "functional-red-black-tree": "^1.0.1", "glob-parent": "^5.1.2", "globals": "^13.6.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "js-yaml": "^3.13.1", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.0.4", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "progress": "^2.0.0", "regexpp": "^3.1.0", "semver": "^7.2.1", "strip-ansi": "^6.0.0", "strip-json-comments": "^3.1.0", "table": "^6.0.9", "text-table": "^0.2.0", "v8-compile-cache": "^2.0.3"}, "devDependencies": {"@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "babel-loader": "^8.0.5", "chai": "^4.0.1", "cheerio": "^0.22.0", "common-tags": "^1.8.0", "core-js": "^3.1.3", "dateformat": "^3.0.3", "ejs": "^3.0.2", "eslint": "file:.", "eslint-config-eslint": "file:packages/eslint-config-eslint", "eslint-plugin-eslint-plugin": "^3.5.3", "eslint-plugin-internal-rules": "file:tools/internal-rules", "eslint-plugin-jsdoc": "^25.4.3", "eslint-plugin-node": "^11.1.0", "eslint-release": "^2.0.0", "eslump": "^3.0.0", "esprima": "^4.0.1", "fs-teardown": "0.1.1", "glob": "^7.1.6", "jsdoc": "^3.5.5", "karma": "^6.1.1", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-webpack": "^5.0.0", "lint-staged": "^10.1.2", "load-perf": "^0.2.0", "markdownlint": "^0.19.0", "markdownlint-cli": "^0.22.0", "memfs": "^3.0.1", "mocha": "^8.3.2", "mocha-junit-reporter": "^2.0.0", "node-polyfill-webpack-plugin": "^1.0.3", "npm-license": "^0.3.3", "nyc": "^15.0.1", "proxyquire": "^2.0.1", "puppeteer": "^7.1.0", "recast": "^0.19.0", "regenerator-runtime": "^0.13.2", "shelljs": "^0.8.2", "sinon": "^9.0.1", "temp": "^0.9.0", "webpack": "^5.23.0", "webpack-cli": "^4.5.0", "yorkie": "^2.0.0"}, "keywords": ["ast", "lint", "javascript", "ecmascript", "espree"], "license": "MIT", "engines": {"node": "^10.12.0 || >=12.0.0"}}