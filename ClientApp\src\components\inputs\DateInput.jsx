import React, { useState, useEffect, useRef } from "react"
import { focusStyles } from "../shared/inputs"

import { Label } from "./Label"

const labelStyles = "flex flex-col mb-2.5"
const labelTextStyles = "pb-2.5"
const borderStyles = "border-solid border-2 bg-white shadow-inner"
const monthAndDayInputStyles = `block w-20 h-14 p-2.5 ${focusStyles} ${borderStyles}`
const yearInputStyles = `block w-32 h-14 p-2.5 ${focusStyles} ${borderStyles}`

const DateInput = ({
  id,
  value: dateString, // There's a lot of `value` variables in this component.
  onChange,
  error,
  displayFormat,
  ...dateInputProps
}) => {
  const borderStylesCheck = error ? "border-red-600" : "border-gray-500"
  const [date, setDate] = useState({ month: "", day: "", year: "" })
  const [elemId, setElemId] = useState("")

  const handleClose = e => {
    let clickedInsideMenu = e.target.closest("#" + id) === undefined
    if (!clickedInsideMenu) {
      setElemId("")
    }
  }
  useEffect(() => {
    if (dateString) setDate(dateFromString(dateString, displayFormat))
    document.addEventListener("click", handleClose, true)
    return () => {
      document.removeEventListener("click", handleClose, true)
    }
  }, [dateString, displayFormat])

  const updateState = event => {
    const { value, name, id, min, max, minLength, maxLength } = event.target
    let maxVl = parseInt(max)
    let minVl = parseInt(min)
    let newVal = value
    if ((name === "month" || name === "day") && newVal > maxVl) {
      newVal = max
    } else if ((name === "month" || name === "day") && newVal < minVl) {
      newVal = `0${min}`
    } else if ((name === "month" || name === "day") && newVal.length === 1) {
      newVal = `0${newVal}`
    } else if (
      name === "year" &&
      newVal.length === maxLength &&
      newVal > maxVl
    ) {
      newVal = max
    } else if (
      name === "year" &&
      newVal.length === maxLength &&
      newVal < minVl
    ) {
      newVal = minVl
    } else if ((name === "month" || name === "day") && newVal.length > 1) {
      while (newVal.charAt(0) === "0" && newVal.length > 1) {
        newVal = newVal.substring(1)
      }
    }
    const newDate = { ...date, [name]: newVal }
    setElemId(id)
    setDate(newDate)
    onChange(dateToString(newDate, displayFormat))
  }

  const Fields = ({ displayFormat, required }) => {
    const Month = ({ value }) => (
      <MonthInput
        id={`${id}-month`}
        value={value}
        required={required}
        handleInputChange={updateState}
        styleBorder={borderStylesCheck}
        elemId={elemId}
      />
    )
    const Day = ({ value }) => (
      <DayInput
        id={`${id}-day`}
        value={value}
        required={required}
        handleInputChange={updateState}
        styleBorder={borderStylesCheck}
        elemId={elemId}
      />
    )
    const Year = ({ value }) => (
      <YearInput
        id={`${id}-year`}
        value={value}
        required={required}
        handleInputChange={updateState}
        styleBorder={borderStylesCheck}
        elemId={elemId}
      />
    )

    const { day, month, year } = date
    switch (displayFormat) {
      case "mm/yyyy":
        return (
          <>
            {<Month value={month} />}
            {<Year value={year} />}
          </>
        )
      case "yyyy":
        return <Year value={year} />
      default:
        return (
          <>
            {<Month value={month} />}
            {<Day value={day} />}
            {<Year value={year} />}
          </>
        )
    }
  }
  const dateFromString = (dateString, displayFormat) => {
    let [part1, part2, part3] = dateString.split("/")

    switch (displayFormat) {
      case "mm/yyyy":
        return { month: part1, year: part2 }
      case "yyyy":
        return { year: part1 }
      default:
        return { day: part2, month: part1, year: part3 }
    }
  }

  const dateToString = (dateObject, displayFormat) => {
    switch (displayFormat) {
      case "mm/yyyy":
        return `${dateObject.month}/${dateObject.year}`
      case "yyyy":
        return dateObject.year
      default:
        return `${dateObject.month}/${dateObject.day}/${dateObject.year}`
    }
  }

  const MonthInput = ({
    id,
    value,
    required,
    handleInputChange,
    styleBorder,
    elemId,
  }) => {
    return (
      <Label className={labelStyles}>
        <span className={labelTextStyles}>Month</span>
        <input
          ref={input => {
            if (id == elemId && input != null) {
              input.focus()
            }
          }}
          className={`${monthAndDayInputStyles} ${styleBorder}`}
          type="number"
          id={id}
          value={value}
          name="month"
          min="1"
          max="12"
          minLength="1"
          maxLength="2"
          required={required}
          onChange={handleInputChange}
        />
      </Label>
    )
  }

  const DayInput = ({
    id,
    value,
    required,
    handleInputChange,
    styleBorder,
    elemId,
  }) => {
    return (
      <Label className={labelStyles}>
        <span className={labelTextStyles}>Day</span>
        <input
          ref={input => {
            if (id == elemId && input != null) {
              input.focus()
            }
          }}
          className={`${monthAndDayInputStyles} ${styleBorder}`}
          type="number"
          id={id}
          value={value}
          name="day"
          min="1"
          max="31"
          minLength="1"
          maxLength="2"
          required={required}
          onChange={handleInputChange}
        />
      </Label>
    )
  }

  const YearInput = ({
    id,
    value,
    required,
    handleInputChange,
    styleBorder,
    elemId,
  }) => {
    return (
      <Label className={labelStyles}>
        <span className={labelTextStyles}>Year</span>
        <input
          ref={input => {
            if (id == elemId && input != null) {
              input.focus()
            }
          }}
          className={`${yearInputStyles} ${styleBorder}`}
          type="number"
          id={id}
          value={value}
          name="year"
          min="1920"
          max="2099"
          minLength="4"
          maxLength="4"
          required={required}
          onChange={handleInputChange}
        />
      </Label>
    )
  }

  return (
    <div id={id} className="flex flex-row space-x-2.5" {...dateInputProps}>
      <Fields displayFormat={displayFormat} {...dateInputProps} />
    </div>
  )
}
export default DateInput
