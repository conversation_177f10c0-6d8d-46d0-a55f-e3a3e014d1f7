import React from "react";
import { useStaticQuery, graphql } from "gatsby";
import { Button } from "./Button";

const Card = ({
  completed,
  disabled,
  textContent,
  pageTo,
  length,
  currentPosition,
  onClick,
}) => {
  let msg, btn, variant, clr, bgClr, clrText;
  if (completed === "Y") {
    msg = "The section is complete.";
    btn = "Edit Answers";
    variant = "secondary";
    bgClr = "bg-green-100 border-green-700";
    clr = "text-green-700 font-bold";
  } else if (completed === "N") {
    msg = "Start this section.";
    btn = "Start Section";
    variant = "primary";
    bgClr = "bg-white";
  } else {
    msg = "The section is incomplete.";
    btn = "Continue Section";
    bgClr = "bg-white";
    variant = "primary";
  }
  if (disabled === "Y") {
    bgClr = "bg-gray-100 border-gray-400";
    variant = "primary";
    clrText = "text-gray-400";
  }
  function CompletedIcon() {
    return (
      <img
        src={process.env.IMG_URL + data.checkmark.publicURL}
        alt="Completed"
        height={18}
        width={23}
        className="inline "
      />
    );
  }
  const data = useStaticQuery(graphql`
    query {
      checkmark: file(relativePath: { eq: "checkmark.svg" }) {
        name
        publicURL
      }
    }
  `);
  return (
    <div className={`mx-auto space-y-5 rounded-md border-2 p-3 ${bgClr}`}>
      <div
        className={`flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row ${clrText}`}
      >
        <h2 className="font-bold">{textContent}</h2>
        <div>{`Section ${currentPosition + 1} of ${length}`}</div>
      </div>
      <div className={clr}>
        {completed === "Y" && (
          <>
            <CompletedIcon /> &nbsp;
          </>
        )}
        {msg}
      </div>
      <Button
        disabled={disabled === "Y" ? true : false}
        variant={variant}
        className="w-full md:w-fit"
        onClick={() => onClick(pageTo)}
        aria-label={textContent + "-" + btn}
        // id={`GoBackBtn${currentPosition + 1}`}
      >
        {btn}
      </Button>
    </div>
  );
};
export default Card;
