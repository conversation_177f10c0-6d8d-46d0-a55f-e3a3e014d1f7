import React from "react";
import { borderStyles, inputStyles, focusStyles } from "../shared/inputs";

export function TextInput({
  error,
  onChange,
  format,
  minLength,
  maxLength,
  ...textInputProps
}) {
  const preventUnwantedCharacters = (event) => {
    const allawedCharacters =
      "abcdefghijklmnopqrstuvwxyz1234567890!#$'()*,+-.:;=?@^_`{|}~%[]&<>";
    let val = event.key.toLowerCase(); //convert to lower case to avoid case sensitive issues.
    if (
      !allawedCharacters.includes(val) &&
      val !== "backspace" &&
      val !== "delete" &&
      val !== " " &&
      val !== "tab"
    ) {
      event.preventDefault();
    }
  };
  return (
    <input
      className={`${inputStyles} ${focusStyles} ${borderStyles(error)}`}
      type={format ? format : "text"}
      onChange={(event) => onChange(event.target.value)}
      minLength={minLength}
      maxLength={maxLength}
      onKeyDown={preventUnwantedCharacters}
      {...textInputProps}
    />
  );
}
