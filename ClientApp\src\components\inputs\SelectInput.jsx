import React from "react"
import { focusStyles } from "../shared/inputs"

export function SelectInput({ error, options, onChange, ...selectProps }) {
  const borderColor = error ? "border-red-600" : "border-gray-500"

  return (
    <select
      {...selectProps}
      onChange={event => onChange(event.target.value)}
      className={`w-full appearance-none border-2 py-3.5 pl-2.5 pr-6 ${borderColor} rounded-none bg-white shadow-inner ${focusStyles}`}
    >
      <EmptyOption />
      <SelectOptions options={options} />
    </select>
  )
}

function EmptyOption() {
  return <option value="" />
}

function SelectOptions({ options }) {
  return options.map(({ Value, Display }) => (
    <option key={Value} value={Value}>
      {Display}
    </option>
  ))
}
