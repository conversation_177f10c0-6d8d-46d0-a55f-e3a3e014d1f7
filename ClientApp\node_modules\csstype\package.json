{"name": "csstype", "version": "3.1.1", "main": "", "types": "index.d.ts", "description": "Strict TypeScript and Flow types for style based on MDN data", "repository": "https://github.com/frenic/csstype", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@types/chokidar": "^2.1.3", "@types/css-tree": "^1.0.7", "@types/jest": "^27.5.1", "@types/jsdom": "^16.2.14", "@types/node": "^17.0.33", "@types/prettier": "^2.6.1", "@types/request": "^2.48.8", "@types/turndown": "^5.0.1", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "eslint": "^8.15.0", "css-tree": "^2.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "fast-glob": "^3.2.11", "flow-bin": "^0.178.0", "jest": "^28.1.0", "jsdom": "^19.0.0", "mdn-browser-compat-data": "git+https://github.com/mdn/browser-compat-data.git#0bf809b28f3cf5007a29b59b885dfada560ead47", "mdn-data": "git+https://github.com/mdn/data.git#9fc21e14442ed6f71626e763e9f5e5370c533380", "prettier": "^2.6.2", "request": "^2.88.2", "ts-jest": "^28.0.2", "ts-node": "^10.7.0", "turndown": "^7.1.1", "typescript": "~4.6.4"}, "scripts": {"prepublish": "npm install --prefix __tests__ && npm install --prefix __tests__/__fixtures__", "prepublishOnly": "tsc && npm run test:src && npm run build && ts-node --files prepublish.ts", "update": "ts-node --files update.ts", "build": "ts-node --files build.ts --start", "watch": "ts-node --files build.ts --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "pretty": "prettier --write build.ts **/*.{ts,js,json,md}", "lazy": "tsc && npm run lint", "test": "jest", "test:src": "jest src.*.ts", "test:dist": "jest dist.*.ts"}, "files": ["index.d.ts", "index.js.flow"], "keywords": ["css", "style", "typescript", "flow", "typings", "types", "definitions"]}