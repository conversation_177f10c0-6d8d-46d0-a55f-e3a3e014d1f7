import React, { useState } from "react"
import { InputBox } from "../shared/InputBox"

const Checkbox = ({ label, onChange, ...inputProps }) => {
  const [isChecked, setIsChecked] = useState(
    inputProps.value === "Y" ? true : false
  )
  return (
    <InputBox
      label={label}
      checked={isChecked}
      disabled={inputProps.disabled}
      error={inputProps.error}
      required={inputProps.required}
    >
      <input
        onChange={event => {
          const { checked } = event.target
          setIsChecked(checked)
          let vl = checked ? "Y" : ""
          onChange(vl)
        }}
        className="mr-2.5 scale-[1.375] accent-sky-500"
        type="checkbox"
        checked={isChecked}
        {...inputProps}
      />
    </InputBox>
  )
}
export default Checkbox
