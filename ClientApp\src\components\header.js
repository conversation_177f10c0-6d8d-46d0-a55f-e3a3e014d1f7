import React, { useState, useEffect } from "react";
import { Link, useStaticQuery, graphql, navigate } from "gatsby";
import useUserStore from "../utils/storeApp";

export default function Header({ ...props }) {
  const [skip, SetSkip] = useState(
    typeof window !== "undefined" ? window.location.href : ""
  );
  const [isFocused, setIsFocused] = useState(false);
  const [isOpen, SetIsOpen] = useState(null);
  const {
    isUserLogedIn,
    userForm,
    setUserLogedIn,
    setUserForm,
    setpageFormUrl,
    setpageUrl,
    setBackPageButton,
    setPageIndex,
    setMoneyObj,
    hideLogin,
  } = useUserStore((state) => ({
    isUserLogedIn: state.isUserLogedIn,
    userForm: state.userForm,
    setUserLogedIn: state.setUserLogedIn,
    setUserForm: state.setUserForm,
    setpageFormUrl: state.setpageFormUrl,
    setpageUrl: state.setpageUrl,
    setBackPageButton: state.setBackPageButton,
    setPageIndex: state.setPageIndex,
    setMoneyObj: state.setMoneyObj,
    hideLogin: state.hideLogin,
  }));

  const handleClose = (e) => {
    let clickedInsideMenu = e.target.closest("#menu") === undefined;
    if (!clickedInsideMenu) {
      SetIsOpen(false);
    }
  };
  const handleLogin = () => {
    let AppId = userForm.ApplicationId;
    setUserLogedIn(false);
    setUserForm({});
    setBackPageButton([]);
    setMoneyObj([]);
    setpageFormUrl("");
    setPageIndex("");
    setpageUrl("");
    navigate(
      "https://account.collegeboard.org/login/logout?appId=7&DURL=https://student.collegeboard.org/css-financial-aid-profile"
    );
  };
  const activeStyles = {
    color: "#e9d5ff",
    textDecorationLine: "underline",
    textUnderlineOffset: "5px",
  };
  useEffect(() => {
    document.addEventListener("click", handleClose, true);
    return () => {
      document.removeEventListener("click", handleClose, true);
    };
  }, []);

  const data = useStaticQuery(graphql`
    query {
      logo: file(relativePath: { eq: "logo.svg" }) {
        name
        publicURL
      }
      profile: file(relativePath: { eq: "profile.jpg" }) {
        name
        publicURL
      }
    }
  `);
  const goto = (url) => {
    navigate(url);
  };
  const handleSkip = () => {
    let btn = document.getElementById("GoBackBtn1");
    if (btn) {
      btn.focus();
      btn.checked = true;
    }
  };
  return (
    <>
      <nav
        className="fixed z-50 flex mb-8 w-full flex-row flex-nowrap items-center justify-center bg-black text-sm font-medium text-white md:text-lg"
        {...props}
      >
        {/* {skip.includes("checkList") ? ( */}
        <a
          className="text-sm"
          href="#nolinkID"
          onClick={handleSkip}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        >
          {isFocused ? "Skip to main content" : null}
        </a>
        {/* ) : null} */}
        <a
          className="pl-2 hidden grow pr-4 md:visible md:flex hover:text-gray-300"
          href="https://www.collegeboard.org"
          target="_blank"
          id="CollegeBoardId"
        >
          <span className="flex flex-row flex-nowrap items-center justify-center space-x-2 pr-1 hover:w-8">
            <img
              src={process.env.IMG_URL + data.logo.publicURL}
              alt="College Board"
              className="inline"
              width={32}
              height={32}
            />
          </span>
          <span>CollegeBoard</span>
        </a>
        <div className="flex w-full flex-row flex-nowrap items-center justify-start bg-program-core-higher-ed p-4">
          <span className="sr-only">CSS Profile</span>
          <div className="flex items-center justify-center">
            <img
              src={process.env.IMG_URL + data.profile.publicURL}
              alt="CSS Profile"
              className="inline"
              width={116}
              height={36}
            />
          </div>

          <button className="ml-auto md:hidden" onClick={() => SetIsOpen(true)}>
            Menu
          </button>
          <span className="ml-auto hidden grow flex-row flex-nowrap items-center justify-end space-x-9 md:visible md:flex">
            <a
              href="https://privacy.collegeboard.org"
              target="_blank"
              className={`hover:text-purple-300`}
            >
              Privacy Policy
            </a>
            <a
              href="https://cssprofile.collegeboard.org/contact-us"
              target="_blank"
              className={`hover:text-purple-300`}
            >
              Contact Us
            </a>
            {hideLogin ? (
              ""
            ) : (
              <>
                {" "}
                <Link
                  to="/dashboard"
                  activeStyle={activeStyles}
                  className={`hover:text-purple-300 cursor-pointer`}
                >
                  Dashboard
                </Link>
                {isUserLogedIn ? (
                  <a
                    href="#nolinkID"
                    className={`hover:text-purple-300 cursor-pointer`}
                    onClick={() => handleLogin()}
                  >
                    Log out
                  </a>
                ) : (
                  <Link
                    to="/login"
                    className={`hover:text-purple-300 cursor-pointer`}
                    activeStyle={activeStyles}
                  >
                    Login
                  </Link>
                )}
              </>
            )}
          </span>
        </div>
      </nav>

      <div className={`${isOpen ? "block" : "hidden"}`}>
        <div className="fixed bottom-0 left-0 right-0 top-0 z-10 bg-black opacity-20"></div>
        <div
          id="menu"
          className="duration-600 fixed top-0 right-0 z-30 z-20 h-full w-64 transform overflow-auto bg-white bg-white text-black transition-all ease-in-out md:hidden"
        >
          <div className="flex justify-between p-5">
            <span className="font-bold">Menu</span>
            <span
              className="ml-20 cursor-pointer font-bold text-sky-700 underline"
              onClick={() => SetIsOpen(false)}
            >
              Close
            </span>
          </div>
          <ul className="flex flex-col gap-y-3 p-5 font-bold">
            <li>
              <a
                href="https://privacy.collegeboard.org"
                target="_blank"
                className={`text-program-core-higher-ed underline underline-offset-2`}
              >
                Privacy Policy
              </a>
            </li>
            <li>
              <div
                onClick={() => goto("/dashboard")}
                className={`text-program-core-higher-ed underline underline-offset-2 cursor-pointer`}
              >
                Dashboard
              </div>
            </li>
            <li>
              <a
                href="https://cssprofile.collegeboard.org/contact-us"
                target="_blank"
                className={`text-program-core-higher-ed underline underline-offset-2`}
              >
                Contact Us
              </a>
            </li>
            <li>
              {isUserLogedIn ? (
                <a
                  href="#nolinkID"
                  className="cursor-pointer text-program-core-higher-ed underline underline-offset-2"
                  onClick={() => handleLogin()}
                >
                  Log out
                </a>
              ) : (
                <div
                  onClick={() => goto("/login")}
                  className={`text-program-core-higher-ed underline underline-offset-2`}
                >
                  Login
                </div>
              )}
            </li>
          </ul>
        </div>
      </div>
    </>
  );
}
