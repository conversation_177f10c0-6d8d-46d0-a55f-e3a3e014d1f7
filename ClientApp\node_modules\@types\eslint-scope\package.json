{"name": "@types/eslint-scope", "version": "3.7.4", "description": "TypeScript definitions for eslint-scope", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint-scope", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/mysticatea", "githubUsername": "mysticatea"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint-scope"}, "scripts": {}, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}, "typesPublisherContentHash": "81c8e26e146b6b132a88bc06480ec59c5006561f35388cbc65756710cd486f05", "typeScriptVersion": "4.0"}