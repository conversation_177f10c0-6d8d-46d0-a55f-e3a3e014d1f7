{"name": "@types/prop-types", "version": "15.7.5", "description": "TypeScript definitions for prop-types", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/prop-types", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ferdaber", "githubUsername": "ferd<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/prop-types"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "771faec3cc5b1aa1cefc03c5dd3668980da8a0c59785867e473d6d7baea31a8a", "typeScriptVersion": "3.9"}