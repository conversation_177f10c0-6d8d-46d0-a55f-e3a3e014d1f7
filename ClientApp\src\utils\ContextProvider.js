import React from "react"
export const GlobalStateContext = React.createContext()
export const GlobalDispatchContext = React.createContext()

const initialState = {
  isUserLogedIn: false,
  formURL: "",
  userForm: {},
  userObject: {},
}

function reducer(state, action) {
  switch (action.type) {
    case "UPDATE_PAGE": {
      return {
        ...state,
        formURL: action.payload,
      }
    }
    case "USERS_DATA": {
      return {
        ...state,
        isUserLogedIn: action.payload,
      }
    }
    case "UPDATE_FORM": {
      return {
        ...state,
        userForm: action.payload,
      }
    }
    case "UPDATE_OBJECT": {
      return {
        ...state,
        userObject: action.payload,
      }
    }
    default:
      return state
  }
}

const GlobalContextProvider = ({ children }) => {
  const [state, dispatch] = React.useReducer(reducer, initialState)

  return (
    <GlobalStateContext.Provider value={state}>
      <GlobalDispatchContext.Provider value={dispatch}>
        {children}
      </GlobalDispatchContext.Provider>
    </GlobalStateContext.Provider>
  )
}

export default GlobalContextProvider
