{"name": "@types/scheduler", "version": "0.16.2", "description": "TypeScript definitions for scheduler", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/Methuselah96", "githubUsername": "Methuselah96"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/scheduler"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "122d740959245799b89613cc799b1a2e3274d1ee1db6c9abd7b6e4dadc0696ec", "typeScriptVersion": "3.6"}