import axios from "axios"

const GetApiResponse = async (url, obj) => {
  const bodyFormData = new FormData()
  bodyFormData.append("sbr", JSON.stringify(obj))
  const formObj = await axios({
    method: "post",
    url: process.env.API_URL + url,
    data: bodyFormData,
    headers: { "Content-Type": "multipart/form-data" },
  })
    .then(resp => {
      const res = resp.data
      if (
        res.ReturnMessage.toUpperCase() === "SUCCESS" ||
        res.ReturnMessage === ""
      ) {
        return {
          status: "SUCCESS",
          data: res,
        }
      } else if (res.ReturnMessage.toUpperCase() === "SERVERERROR") {
        return {
          status: "SERVERERROR",
          data: "Server error has occured.",
        }
      } else if (res.ReturnMessage.toUpperCase() === "FAILEDVALIDATION") {
        return { status: "FAILED", data: res }
      }
    })
    .catch(err => {
      console.log("URL:" + process.env.API_URL + url)
      console.log("OBJ:" + obj)
      return { status: "SERVERERROR", data: "Server error has occured." }
    })

  return formObj
}

export async function saveFormPage(userForm, answersObj) {
  const Questions = !answersObj ? false : GetQuestions(answersObj)
  const postForm = Questions ? { ...userForm, Questions } : userForm
  const response = await GetApiResponse("PostFormPageSave", postForm)
  return response
}
export async function saveFormPageMultiple(url, userForm) {
  const response = await GetApiResponse(url, userForm)
  return response
}
const GetQuestions = answersObj => {
  let arr = []
  for (const [key, value] of Object.entries(answersObj)) {
    const obj = { Id: key, Response: value }
    arr.push(obj)
  }
  return arr
}
const studentInfoSection = [
  "StudentInfoIntro",
  "ParentIncomeSetup1",
  "StudentInfo1",
  "StudentInfo2",
  "StudentInfo3",
  "StudentInfoCit1",
  "StudentInfoCit2",
  "StudentInfo4",
  "StudentInfo5",
  "StudentInfo6",
  "AddressInfo1",
  "StudentInfo10",
  "StudentInfo11",
  "ConfirmDemographics",
  "ParentalRelationships",
  "ParentalRelationships2",
  "ParentalRelationships5",
  "ParentInfo",
  "StudentIncomeResources",
  "StudentIncomeBenefitsIntl",
  "StudentIncomeBenefitsExpected",
  "SectionReview",
  "ParentAssetInfo7",
  "ParentAssetInfo8",
]

function getNextPageName(currentPageName) {
  if (studentInfoSection.includes(currentPageName)) {
    return nextStepForSection(studentInfoSection, currentPageName)
  }
  return null
}

function nextStepForSection(flow, currentPageName) {
  const currentPageIndex = flow.findIndex(
    pageName => pageName === currentPageName
  )
  return flow[currentPageIndex + 1]
}
// Normalize the JSON from the API into something more consistent with JavaScript naming.
export function checklistPageFromJSON(formPageJSON) {
  const questions = extractCardFields(formPageJSON)
  return {
    name: formPageJSON.PageName,
    title: formPageJSON.PageHeading,
    text: formPageJSON.PageText,
    footHeading: formPageJSON.PageFootHeading,
    footText: formPageJSON.PageFootText,
    isCompleted: formPageJSON.AppCompleted,
    heading: extractFormPageText(formPageJSON),
    review: formPageJSON.PageFootSubmit,
    questions,
  }
}
export function extractCardFields(formPageJSON) {
  // I'm assuming there will be only one "text" item in each page.
  const pageTextField = formPageJSON.FWFormItemList
  return pageTextField || null
}
export function sectionReviewPageFromJSON(formPageJSON) {
  const pageTextField = formPageJSON.FWFormItemList
  return {
    title: formPageJSON.PageHeading,
    items: pageTextField,
  }
}
export function multiplePageFromJSON(obj, dicts, chck) {
  const FWFormItemList = obj.FWFormItemList
  const questionAnswers = chck ? extractQuestionAnswers(FWFormItemList) : null
  return {
    SectionHeading: obj.SectionHeading,
    heading: extractFormPageText(obj),
    ItemNumber: obj.ItemNumber,
    dicts: dicts,
    questionAnswers,
    FWFormItemList,
  }
}
export function formPageFromJSON(formPageJSON) {
  const questions = extractFormPageQuestions(formPageJSON)
  const questionAnswers = extractQuestionAnswers(questions)

  return {
    name: formPageJSON.PageName,
    heading: extractFormPageText(formPageJSON),
    title: formPageJSON.PageHeading,
    dicts: formPageJSON.FWDictsList.Dicts,
    questionAnswers,
    questions,
    isReviewPage: formPageJSON?.ReviewPage || "",
    pageText: formPageJSON?.PageText || "",
  }
}
function normalizeAPIError(error) {
  function errorListToErrorObject(questionErrors) {
    let errors = {}
    questionErrors.forEach(questionError => {
      // This assumes there's only one error per question.
      errors[questionError.Id] = questionError.ValidationError
    })
    return errors
  }
  return {
    pageName: error.PageName,
    questionErrors: errorListToErrorObject(error.FWQuestionResponseErrorList),
  }
}

function extractFormPageText(formPageJSON) {
  // I'm assuming there will be only one "text" item in each page.
  const pageTextField = formPageJSON.FWFormItemList.find(
    item => item.itemType === "text"
  )
  return pageTextField || null
}

function extractFormPageQuestions(formPageJSON) {
  return formPageJSON.FWFormItemList.filter(
    item => item.itemType === "question" || item.itemType === "button"
  )
}

function extractQuestionAnswers(questions) {
  const answers = {}

  questions.forEach(question => {
    answers[question.id] = question.questionValue
  })
  return answers
}
