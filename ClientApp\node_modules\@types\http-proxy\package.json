{"name": "@types/http-proxy", "version": "1.17.12", "description": "TypeScript definitions for node-http-proxy", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-proxy", "license": "MIT", "contributors": [{"name": "Maxime LUCE", "url": "https://github.com/SomaticIT", "githubUsername": "SomaticIT"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Raigen", "githubUsername": "Raigen"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jabreu610", "githubUsername": "jabreu610"}, {"name": "<PERSON>", "url": "https://github.com/bodinsamuel", "githubUsername": "bod<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/http-proxy"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "c793ee08cc497092025f11e62b32c2ef880bb8b2e67419374091e6240a455631", "typeScriptVersion": "4.5"}