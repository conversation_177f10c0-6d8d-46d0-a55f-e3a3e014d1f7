import React, { useState, useEffect } from "react";
import { borderStyles, inputStyles, focusStyles } from "../shared/inputs";
import useUserStore from "../../utils/storeApp";

export function SSNInput({
  error,
  onChange,
  format,
  value: dateString,
  minLength,
  maxLength,
  ...textInputProps
}) {
  const { setSSNValue } = useUserStore((state) => ({
    setSSNValue: state.setSSNValue,
  }));
  const [passwordShown, setPasswordShown] = useState(true);
  const [val, setVal] = useState(null);
  const [valPrev, setPrevVal] = useState(null);
  const [lbl, setlbl] = useState(`show`);
  const togglePassword = () => {
    setPasswordShown(!passwordShown);
    if (lbl === `show`) {
      setlbl("hide");
      FormatSSN(valPrev, false);
    } else {
      setlbl(`show`);
      FormatSSN(valPrev, true);
    }
  };
  useEffect(() => {
    if (dateString) {
      FormatSSN(dateString, true);
      setPrevVal(dateString);
    }
  }, []);
  const FormatSSN = async (e, flag, elem, inputChange) => {
    const keyPressed = elem?.key;
    if (keyPressed !== "Tab") {
      let newValue = e;
      if (inputChange) {
        let text = "";
        if (keyPressed == "Backspace") {
          text = valPrev.substring(0, e.length - 1);
        }
        if (keyPressed?.length == 1) {
          text = valPrev + keyPressed;
        }
        newValue = text.replace(/[^0-9]/g, "");
      } else {
        newValue = newValue ? newValue.replace(/[^0-9]/g, "") : "";
      }
      if (newValue.length > 2 && newValue.length < 5) {
        if (keyPressed == "Backspace" && newValue.length == 3) {
          newValue = newValue.substring(0, 3);
        } else {
          newValue = newValue.substring(0, 3) + "-" + newValue.substring(3);
        }
      } else if (newValue.length >= 5) {
        if (keyPressed == "Backspace" && newValue.length == 5) {
          newValue = newValue.substring(0, 3) + "-" + newValue.substring(3, 5);
        } else {
          newValue =
            newValue.substring(0, 3) +
            "-" +
            newValue.substring(3, 5) +
            "-" +
            newValue.substring(5, 9);
        }
      }
      await setPrevVal(newValue);
      await setSSNValue(newValue);
      if (flag) {
        newValue = newValue.replace(/\d/g, "X");
      }
      setVal(newValue);
    }
  };
  const lblStyles =
    "text-black bg-gray-300 hover:bg-gray-400 border-2 border-b-4 border-black rounded-sm px-2 py-1 text-sm font-bold not(:disabled):hover:cursor-pointer not(:disabled):active:outline active:outline-offset-2 active:outline-4 active:outline-yellow-600";
  return (
    <div className="relative w-full">
      <div className="absolute inset-y-0 right-0 flex items-center px-2 py-4">
        <button type="button" className={lblStyles} onClick={togglePassword}>
          {lbl}
        </button>
      </div>
      <input
        className={`${inputStyles} ${focusStyles} ${borderStyles(error)}`}
        // type={!passwordShown ? "text" : "password"}
        onChange={(event) => {
          onChange(event.target.value, valPrev);
        }}
        minLength={minLength}
        maxLength={11}
        {...textInputProps}
        autoComplete="off"
        value={val}
        onKeyDown={(event) =>
          FormatSSN(
            event.target.value,
            passwordShown ? true : false,
            event,
            true
          )
        }
      />
    </div>
  );
}
