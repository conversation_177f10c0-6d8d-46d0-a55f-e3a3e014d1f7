{"name": "@types/common-tags", "version": "1.8.2", "description": "TypeScript definitions for common-tags", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/common-tags", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/zuzusik", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/tzupengwang", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/common-tags"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "d7d27b5f9bcbe65add116098b784aac1c7185e76940f9f85ef8657fe019476a5", "typeScriptVersion": "4.3"}