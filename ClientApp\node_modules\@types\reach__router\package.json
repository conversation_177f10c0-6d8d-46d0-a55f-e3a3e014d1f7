{"name": "@types/reach__router", "version": "1.3.12", "description": "TypeScript definitions for @reach/router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/reach__router", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/xMokAx", "githubUsername": "xMokAx"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/awwit", "githubUsername": "awwit"}, {"name": "wroughtec", "url": "https://github.com/wroughtec", "githubUsername": "wroughtec"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/chilledoj", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Eyas", "githubUsername": "<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/reach__router"}, "scripts": {}, "dependencies": {"@types/react": "*"}, "typesPublisherContentHash": "bd19b954b20351fb0967891be4b996dca0f871cc7e8cb07455d1c543b3fbb911", "typeScriptVersion": "4.5"}