import React, { useContext } from "react"
import { navigate } from "gatsby"
import { buttonClasses } from "./shared/buttons"
import { focusStyles } from "./shared/inputs"
// import { GlobalDispatchContext } from "../utils/ContextProvider"
import useUserStore from "../utils/storeApp"

const ButtonLink = ({
  children,
  href,
  className = "",
  variant = "primary",
  ...props
}) => {
  const { setpageFormUrl } = useUserStore(state => ({
    setpageFormUrl: state.setpageFormUrl,
  }))
  //const dispatch = useContext(GlobalDispatchContext)
  const nav = href => {
    const params = href.split("/")
    if (params[1]) setpageFormUrl(params[1])
    navigate(params[0])
  }
  return (
    <div
      className={`${buttonClasses({ variant })} ${focusStyles} cursor-pointer`}
      {...props}
    >
      <p onClick={() => nav(href)}>{children}</p>
    </div>
  )
}
export default ButtonLink
