import React, { useContext, useEffect, useState } from "react";
import { navigate } from "gatsby";
import Layout from "../components/layout";
import Helmet from "react-helmet";
import { useQueryParam, StringParam } from "use-query-params";
import useUserStore from "../utils/storeApp";
import axios from "axios";
import { ProcessingModal } from "../components/processingModal";
import { Label } from "../components/inputs/Label";
import { ValidationError } from "../components/ValidationError";

const Component = ({ name, children, isRequired, ...props }) => {
  return (
    <>
      <Label required={isRequired} {...props}>
        {name}
      </Label>
      {children}
    </>
  );
};

export default function Home() {
  const {
    setUserLogedIn,
    setpageFormUrl,
    setUserForm,
    currentPageURL,
    userForm,
    setAppPool,
    setHideLogin,
  } = useUserStore((state) => ({
    setUserLogedIn: state.setUserLogedIn,
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    currentPageURL: state.currentPageURL,
    userForm: state.userForm,
    setAppPool: state.setAppPool,
    setHideLogin: state.setHideLogin,
  }));

  const [pageType, setPageType] = useQueryParam("PageType", StringParam);
  const [allowUser, SetAllowUser] = useQueryParam("AllowUser", StringParam);
  const [bodyData, SetbodyData] = useQueryParam("bodyData", StringParam);
  const [state, SetState] = useQueryParam("state", StringParam);
  const [urlStr, SeturlStr] = useQueryParam("urlStr", StringParam);
  const [code, SetCode] = useQueryParam("code", StringParam);
  const [oauthUrl, SetoauthUrl] = useQueryParam("oauthUrl", StringParam);
  const [showModal, setShowModal] = useState(false);
  const [flag, SetFlag] = useState(false);
  const [msg, SetMsg] = useState("Invalid User Login. Please try again.");
  const [loginError, SetLoginError] = useState(false);

  const isEmpty = (vl) => {
    return typeof vl === "undefined" || vl === null || vl === "";
  };
  const logOutFunc = () => {
    navigate(
      "https://account.collegeboard.org/login/logout?appId=&DURL=https://student.collegeboard.org/css-financial-aid-profile"
    );
  };
  useEffect(() => {
    SetLoginError(false);
    SetFlag(false);
    setHideLogin(false);
    const fetchData = async (bodyData, state) => {
      let token = JSON.parse(bodyData);
      let o = { State: state, Code: token.access_token };
      let os = JSON.stringify(o);

      if (isEmpty(token.access_token)) {
        navigate("https://account.collegeboard.org");
      }
      const bodyFormData = new FormData();
      bodyFormData.append("sbr", os);
      const user = await axios({
        method: "post",
        url: process.env.API_URL + "GetCBLogin", //"GetFWCBLogin",
        data: bodyFormData,
        headers: { "Content-Type": "application/form-data" },
      })
        .then((res) => {
          setShowModal(false);
          if (res.data.ReturnMessage.toUpperCase() === "SUCCESS") {
            setUserForm(res.data);
            setpageFormUrl(res.data.PageName);
            if (res.data.PageName === "Dashboard") {
              setUserLogedIn(true);
              navigate("/dashboard");
            } else if (res.data.PageName === "TermsConditions") {
              setUserLogedIn(true);
              navigate("/terms");
            } else {
              logOutFunc();
            }
          } else if (
            res.data.ReturnMessage.toUpperCase() === "FAILED" ||
            res.data.ReturnMessage === ""
          ) {
            setHideLogin(true);
            navigate("https://account.collegeboard.org");
            return;
          } else {
            SetMsg("Server error has occured.");
            SetFlag(true);
            navigate("https://account.collegeboard.org");
          }
        })
        .catch((err) => {
          setHideLogin(true);
          SetMsg("Server error has occured.");
          SetFlag(true);
          navigate("https://account.collegeboard.org");
        });
    };
    if (!isEmpty(state) && !isEmpty(bodyData)) {
      fetchData(bodyData, state);
    } else if (!isEmpty(oauthUrl)) {
      let url = decodeURIComponent(escape(oauthUrl));
      window.open(url, "_self");
    } else if (!isEmpty(allowUser)) {
      checkUser(allowUser, pageType);
    } else {
      navigate("https://account.collegeboard.org");
    }
  }, []);
  const checkUser = async (vl, page) => {
    await axios({
      method: "get",
      url:
        process.env.IMG_URL + "/GetUser/GetUser?userName=" + vl.toLowerCase(),
      headers: { "Content-Type": "application/json" },
    })
      .then((res) => {
        console.log(res);
        if (res.status === 200 && res.data.status === "Good") {
          if (page === "CreateUser") {
            navigate("/createAccount");
          } else if (page === "login") {
            navigate("/login");
          } else {
            navigate("https://account.collegeboard.org");
          }
          setAppPool(res.data.apppool);
        } else {
          navigate("https://account.collegeboard.org");
        }
      })
      .catch((err) => {
        navigate("https://account.collegeboard.org");
      });
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={"Home"}
      />
      <main className="x-auto space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple mt-12 md:mt-6">
        {flag && (
          <Component name="">
            {flag && <ValidationError message={msg} />}
          </Component>
        )}
        {loginError ? (
          <Component name={"CollegeBoard CSS Profile"}>
            <ol style={{ listStyle: "disc" }}>
              <li>You logged out of the application.</li>
              <li>Inactivity.</li>
              <li>A processing error occurred.</li>
              <li>
                You aren't using a valid College Board student account to access
                the site.
              </li>
            </ol>
            <p>
              Return to{" "}
              <a
                href="https://cssprofile.collegeboard.org/"
                target="blank"
                className="cursor-pointer text-sky-700 underline"
              >
                <b>CSS Profile</b>
              </a>{" "}
              or{" "}
              <a
                href="https://finaid.collegeboard.org/cssprofile-student"
                className="cursor-pointer text-sky-700 underline"
                target="blank"
              >
                <b>Contact Us</b>
              </a>
              .
            </p>
          </Component>
        ) : null}
      </main>
      {showModal ? <ProcessingModal /> : null}
    </Layout>
  );
}
