import React from "react";
import GlobalContextProvider from "./src/utils/ContextProvider";
import "./src/styles/global.css";

// eslint-disable-next-line import/prefer-default-export
export const onClientEntry = () => {
  // Without this function body the import will not be picked up.
};

export const wrapRootElement = ({ element }) => {
  return <GlobalContextProvider>{element}</GlobalContextProvider>;
};
