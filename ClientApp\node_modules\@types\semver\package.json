{"name": "@types/semver", "version": "7.5.3", "description": "TypeScript definitions for semver", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/semver", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/Bartvds", "githubUsername": "Bartvds"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/Lucian<PERSON>zo", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ajafff", "githubUsername": "a<PERSON><PERSON><PERSON>"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss", "githubUsername": "ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/semver"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "899f87e0aba8e7570c139c1d8aa9356942a2d5e12f7612a3ca18ec2f39ef67cd", "typeScriptVersion": "4.5"}