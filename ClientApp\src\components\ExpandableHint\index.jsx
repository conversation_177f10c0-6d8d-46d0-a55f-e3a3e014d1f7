import React from "react"
import { useStatic<PERSON>uery, graphql } from "gatsby"
import { hintClasses } from "../shared/hints"
import { focusWithinStyles } from "../shared/inputs"
import parse from "html-react-parser"

const ExpandableHint = ({ title, ...props }) => {
  const { content } = props
  const data = useStaticQuery(graphql`
    query {
      expand: file(relativePath: { eq: "expand.svg" }) {
        name
        publicURL
      }
      collapse: file(relativePath: { eq: "collapse.svg" }) {
        name
        publicURL
      }
      info: file(relativePath: { eq: "info-icon.svg" }) {
        name
        publicURL
      }
    }
  `)
  return (
    <details
      className={`group flex w-full flex-col justify-center ${hintClasses} ${focusWithinStyles}`}
      {...props}
    >
      <summary className="flex cursor-pointer p-5 focus:outline-none group-open:pb-0">
        <img
          className="text-black"
          src={process.env.IMG_URL + data.info.publicURL}
          alt=""
          height={16}
          width={16}
        />
        <span className="ml-2.5 pl-1">
          {title ? <strong>{parse(title)}</strong> : null}
        </span>

        <div className="ml-auto inline">
          <span className="group-open:hidden">
            <img
              src={process.env.IMG_URL + data.expand.publicURL}
              alt=""
              height={12}
              width={10.8}
            />
          </span>
          <span className="hidden group-open:block">
            <img
              src={process.env.IMG_URL + data.collapse.publicURL}
              alt=""
              height={12}
              width={10.8}
            />
          </span>
        </div>
      </summary>
      <div className="mt-2.5 p-5 space-y-5 pt-0">{content}</div>
    </details>
  )
}
export default ExpandableHint
