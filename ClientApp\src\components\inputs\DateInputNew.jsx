import React, { useState, useEffect } from "react";
import { focusStyles } from "../shared/inputs";
import { Label } from "./Label";
import moment from "moment";

const labelStyles = "flex flex-col mb-2.5";
const labelTextStyles = "pb-2.5";
const borderStyles = "border-solid border-2 bg-white shadow-inner";
const DayInputStyles = `block w-20 h-14 p-2.5 ${focusStyles} ${borderStyles}`;
const monthStyles = `w-full h-14 appearance-none border-2 py-3.5 pl-2.5 pr-6 ${borderStyles} rounded-none bg-white shadow-inner ${focusStyles}`;
const yearInputStyles = `block w-32 h-14 p-2.5 ${focusStyles} ${borderStyles}`;

const DateInput = ({
  id,
  value: dateString, // There's a lot of `value` variables in this component.
  onChange,
  error,
  displayFormat,
  required,
  ...dateInputProps
}) => {
  const borderStylesCheck = error ? "border-red-600" : "border-gray-500";
  const [date, setDate] = useState({ month: "", day: "", year: "" });
  const [format, setFormat] = useState({ month: true, day: true, year: true });
  useEffect(() => {
    if (dateString) {
      setDate(dateFromString(dateString, displayFormat));
    }
  }, [dateString]);

  const dateFromString = (dateString, displayFormat) => {
    switch (displayFormat) {
      case "mm/yyyy":
        let [mnth, yr] = dateString.split("/");
        return { month: mnth, year: yr };
      case "yyyy":
        return { year: dateString };
      default:
        let [part1, part2, part3] = dateString.split("/");
        return { month: part1, day: part2, year: part3 };
    }
  };
  const dateToString = (dateObject, displayFormat) => {
    switch (displayFormat) {
      case "mm/yyyy":
        return `${dateObject.month}/${dateObject.year}`;
      case "yyyy":
        return dateObject.year;
      default:
        return `${dateObject.month}/${dateObject.day}/${dateObject.year}`;
    }
  };
  const updateState = async (event) => {
    const { value, name, id, min, max, minLength, maxLength } = event.target;
    let maxVl = parseInt(max);
    let minVl = 0;
    let newVal = parseInt(value);

    if (!isNaN(newVal) && newVal > maxVl) {
      newVal = maxVl;
    } else if (!isNaN(newVal) && name !== "year" && newVal < minVl) {
      newVal = minVl;
    } else if (
      !isNaN(newVal) &&
      name == "year" &&
      newVal.toString().length >= 4 &&
      newVal < minVl
    ) {
      newVal = minVl;
    }
    if (name !== "year" && newVal < 10) {
      newVal = `0${newVal}`;
    }
    const newDate = { ...date, [name]: isNaN(newVal) ? "" : newVal };
    setDate(newDate);
    onChange(dateToString(newDate, displayFormat));
  };
  return (
    <>
      {/* <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-3">
        <p>
          <b>Note:</b> Please make sure to enter the correct format as (
          {displayFormat.toUpperCase()}).{" "}
        </p>
      </div> */}
      <div id={id} className="flex flex-row space-x-2.5" {...dateInputProps}>
        {displayFormat !== "yyyy" ? (
          <Label className={labelStyles}>
            <span className={labelTextStyles}>Month</span>
            <select
              className={`${monthStyles} ${borderStylesCheck}`}
              type="number"
              value={date.month}
              aria-describedby={`${id}Desc`}
              name="month"
              min="1"
              max="12"
              minLength="1"
              maxLength="2"
              required={required}
              onChange={(el) => {
                const newDate = { ...date, month: el.target.value };
                setDate(newDate);
                onChange(dateToString(newDate, displayFormat));
              }}
            >
              <option key={61} value={""}></option>
              <option key={6} value={"01"}>
                January
              </option>
              <option key={7} value={"02"}>
                February
              </option>
              <option key={8} value={"03"}>
                March
              </option>
              <option key={1} value={"04"}>
                April
              </option>
              <option key={2} value={"05"}>
                May
              </option>
              <option key={3} value={"06"}>
                June
              </option>
              <option key={4} value={"07"}>
                July
              </option>
              <option key={5} value={"08"}>
                August
              </option>

              <option key={9} value={"09"}>
                September
              </option>
              <option key={10} value={"10"}>
                October
              </option>
              <option key={11} value={"11"}>
                November
              </option>
              <option key={12} value={"12"}>
                December
              </option>
            </select>
          </Label>
        ) : null}
        {displayFormat === "mm/dd/yyyy" || !displayFormat ? (
          <Label className={labelStyles}>
            <span className={labelTextStyles}>Day</span>
            <input
              className={`${DayInputStyles} ${borderStylesCheck}`}
              type="number"
              value={date.day}
              aria-describedby={`${id}Desc`}
              name="day"
              min="1"
              max="31"
              minLength="1"
              maxLength="2"
              required={required}
              onChange={(el) => {
                updateState(el);
              }}
            />
          </Label>
        ) : null}

        <Label className={labelStyles}>
          <span className={labelTextStyles}>Year</span>
          <input
            className={`${yearInputStyles} ${borderStylesCheck}`}
            type="number"
            value={date.year}
            aria-describedby={`${id}Desc`}
            name="year"
            min="1920"
            max="2099"
            minLength="4"
            maxLength="4"
            required={required}
            onChange={(el) => {
              updateState(el);
            }}
          />
        </Label>
      </div>
    </>
  );
};
export default DateInput;
