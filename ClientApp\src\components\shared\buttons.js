const primaryButtonColors =
  "text-white bg-sky-700 hover:bg-sky-600 disabled:border-gray-500 disabled:bg-gray-400";
const secondaryButtonColors =
  "text-black bg-gray-300 hover:bg-gray-400 disabled:border-gray-500 disabled:bg-gray-100 disabled:text-gray-500";
const greenButtonColors = "bg-green-100 text-green-800 hover:bg-green-200";
const whiteButtonColors = "hover:bg-gray-100";
const baseButtonClasses = `py-3 px-6 border-2 border-b-4 border-black rounded-sm text-center font-bold
not(:disabled):hover:cursor-pointer not(:disabled):active:outline active:outline-offset-2 active:outline-4 active:outline-yellow-600`;

export function buttonClasses({ variant }) {
  switch (variant) {
    case "primary":
      return `${baseButtonClasses} ${primaryButtonColors}`;
    case "secondary":
      return `${baseButtonClasses} ${secondaryButtonColors}`;
    case "green":
      return `${baseButtonClasses} ${greenButtonColors}`;
    case "white":
      return `${baseButtonClasses} ${whiteButtonColors}`;
    default:
      return baseButtonClasses;
  }
}
