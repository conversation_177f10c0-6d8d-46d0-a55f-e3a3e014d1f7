{"name": "@types/glob", "version": "5.0.37", "description": "TypeScript definitions for Glob", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/glob", "license": "MIT", "contributors": [{"name": "vvakame", "url": "https://github.com/vvakame", "githubUsername": "vvakame"}, {"name": "voy", "url": "https://github.com/voy", "githubUsername": "voy"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/glob"}, "scripts": {}, "dependencies": {"@types/minimatch": "*", "@types/node": "*"}, "typesPublisherContentHash": "491e9fa3250f87d4ee7b51a8511bd3ec3772dfa52be63e827d35dcf1418e6948", "typeScriptVersion": "3.6"}