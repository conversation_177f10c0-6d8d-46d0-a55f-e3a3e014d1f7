import React, { useEffect, useState } from "react";
import { navigate } from "gatsby";
import {
  formPageFromJSON,
  SaveCertForm,
  getSaveCert,
} from "../components/NewComp";
import Layout from "../components/layout";
import { TextInput } from "../components/inputs/TextInput";
import { SelectInput } from "../components/inputs/SelectInputCode";
import { Label } from "../components/inputs/Label";
import { Button } from "../components/Button";
import Question from "../components/Question";
import parse from "html-react-parser";
import Helmet from "react-helmet";
import { ValidationError } from "../components/ValidationError";
import { dtjson } from "../fakeDB/sectionReview";
import { ProcessingModal } from "../components/processingModal";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";

const Payment = ({ ttl, content }) => {
  const {
    setpageFormUrl,
    userForm,
    isUserLogedIn,
    formURL,
    setUserForm,
    setpageUrl,
    backPageButton,
    pageIndex,
    setPageIndex,
    backButtonMode,
    setBackButtonMode,
  } = useUserStore((state) => ({
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    userForm: state.userForm,
    formURL: state.formURL,
    isUserLogedIn: state.isUserLogedIn,
    setpageUrl: state.setpageUrl,
    backPageButton: state.backPageButton,
    pageIndex: state.pageIndex,
    setPageIndex: state.setPageIndex,
    backButtonMode: state.backButtonMode,
    setBackButtonMode: state.setBackButtonMode,
  }));

  const [flag, SetFlag] = useState(false);
  const [certFlag, SetCertFlag] = useState(false);
  const [disableBtn, SetdisableBtn] = useState(false);
  const [showWarning, SetShowWarning] = useState(false);
  const [urlPage, SetUrlPage] = useState(formURL);
  const [msg, SetMsg] = useState("");
  const [formData, SetFormData] = useState({});
  const [answers, setAnswers] = useState();
  const [answersCode, setAnswersCode] = useState();
  const [answersCodeHHB, setAnswersCodeHHB] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [amountMsg, SetAmountMsg] = useState("");
  const [titlePage, SetTitlePage] = useState("");

  const remove_linebreaks = (str) => {
    return str.replace(/[\r\n]+/gm, "").trim();
  };
  const setAnswer = async (questionId, answer) => {
    await setAnswers({ ...answers, [questionId]: answer });
  };
  const setAnswerCodeHHB = (code, val) => {
    let o = [{ WildCodeCSSCode: code, FeePaymentCode: val }];
    setAnswersCodeHHB(o);
  };
  const setAnswerCode = (nr, answer, type) => {
    let WildCodeCSSCode, FeePaymentCode;
    const temp = [...answersCode];
    if (type === "college") {
      temp[nr].WildCodeCSSCode = answer;
    } else {
      temp[nr].FeePaymentCode = answer;
    }
    setAnswersCode(temp);
  };
  const ValErrors = (flObj) => {
    const failObj = formData.questions.map((el, idx) => {
      flObj.map((elem, ind) => {
        if (elem.Id === el.id) {
          el.validateErrMsg = elem.ErrorMsg;
          //el.required = "Y"
        } else if (elem.Id === "PaymentAmount") {
          SetAmountMsg(elem.ErrorMsg);
        }
      });
      return el;
    });
    SetFormData({ ...formData, questions: failObj });
  };
  const CodeErrors = (type, flObj) => {
    if (type === "HHA") {
      const failObj = answersCode.map((el, idx) => {
        flObj.map((elem, ind) => {
          if (elem.FeePaymentCode === el.FeePaymentCode) {
            el.validateErrMsg = elem.ErrorMsg;
          }
        });
        return el;
      });
      setAnswersCode(failObj);
    } else {
      SetFormData({ ...formData, error: flObj[0].ErrorMsg });
    }
  };
  const onFormSubmition = async (event) => {
    event.preventDefault();
    SetdisableBtn(true);
    setBackButtonMode(false);
    SetAmountMsg("");
    if (urlPage === "CreditDebit") {
      let dateBreak = answers.month_year.split("/");
      let ans = {
        ...userForm,
        ...answers,
        PageName: "CreditCardPayment",
        CardExpirationMonth: dateBreak[0],
        CardExpirationYear: dateBreak[1],
      };
      delete ans.month_year;
      const dts = await SaveCertForm("PostPayment", ans);
      if (dts.status === "SUCCESS") {
        SetFlag(false);
        if (dts.data.ReturnMessage.toUpperCase() === "SUCCESS") {
          navigate("/dashboard");
        } else if (dts.data.ReturnMessage.toUpperCase() === "APPLICATIONPAID") {
          navigate("/finalCharge");
        } else {
          SetMsg(dts.data.ErrorMessage);
          SetFlag(true);
        }
      } else if (dts.status === "FAILED") {
        if (dts.data.ErrorMessage) {
          SetAmountMsg(dts.data.ErrorMessage);
          document.getElementById("scrollToTop").scrollIntoView();
          SetdisableBtn(false);
        } else if (dts.data.QuestionErrorList) {
          ValErrors(dts.data.QuestionErrorList);
        }
      } else if (dts.status === "SERVERERROR") {
        SetFlag(true);
        SetMsg(dts.data);
        SetdisableBtn(false);
      }
      SetdisableBtn(true);
    } else if (urlPage === "CodePayment") {
      const failObj = answersCode.map((el, idx) => {
        delete el.validateErrMsg;
        return el;
      });
      let ansCode = {
        Username: userForm.Username,
        UserId: userForm.UserId,
        AwardYear: userForm.AwardYear,
        ApplicationId: userForm.ApplicationId,
        OrgFormId: userForm.OrgFormId,
        FormId: userForm.FormId,
        ApplicationChargeId: userForm.ApplicationChargeId,
        PageName: "FeePaymentCode",
        FWFeePaymentCodeList:
          formData.FPCView === "HHA" ? failObj : answersCodeHHB,
      };
      const dtsCode = await SaveCertForm("PostFeePaymentCodes", ansCode);
      if (dtsCode.status === "SUCCESS") {
        SetFlag(false);
        if (dtsCode.data.ReturnMessage.toUpperCase() === "SUCCESS") {
          let page =
            dtsCode.data.PageName === "Dashboard" ? "dashboard" : "finalCharge";
          navigate("/" + page);
        } else if (
          dtsCode.data.ReturnMessage.toUpperCase() === "FPCNOTPROVIDED"
        ) {
          navigate("/finalCharge");
        } else {
          SetMsg(dtsCode.data.ErrorMessage);
          SetFlag(true);
        }
      } else if (dtsCode.status === "FAILED") {
        CodeErrors(formData.FPCView, dtsCode.data.FWFeePaymentCodeErrorList);
        SetdisableBtn(false);
      } else if (dtsCode.status === "SERVERERROR") {
        SetFlag(true);
        SetMsg(dtsCode.data);
        SetdisableBtn(false);
      }
    } else if (urlPage === "Correction") {
      let corrTxt = remove_linebreaks(answers.CorrectionExplanation);
      if (corrTxt === "") {
        const failObj = formData.questions.map((el, idx) => {
          if (el.id === "CorrectionExplanation") {
            el.validateErrMsg = "Please fill out this field";
          }
          return el;
        });
        SetFormData({ ...formData, questions: failObj });
        document.getElementById("CorrectionReasonId").scrollIntoView();
        document.getElementById("CorrectionExplanation").focus();
        SetdisableBtn(false);
        return;
      }
      let corrCode = {
        Username: userForm.Username,
        UserId: userForm.UserId,
        AwardYear: userForm.AwardYear,
        ApplicationId: userForm.ApplicationId,
        OrgFormId: userForm.OrgFormId,
        FormId: userForm.FormId,
        PageName: "Correction",
        CorrectionReasonId: answers.CorrectionReasonId,
        CorrectionExplanation: corrTxt,
      };
      if (
        answers.CorrectionReasonId === "1" &&
        userForm.AllowCorrectionExplanationUpdate !== "Y"
      ) {
        SetShowWarning(true);
        document.getElementById("scrollToTop").scrollIntoView();
        document.getElementById("scrollToTop").focus();
        return;
      }
      const corrResp = await getSaveCert("PostCorrection", corrCode);
      if (corrResp.status === "SUCCESS") {
        setUserForm(corrResp.data);
        setpageFormUrl(corrResp.data.PageName);
        if (corrResp.data.Multiple === "Y") {
          navigate("/multiple");
        } else if (corrResp.data.PageName === "ApplicationChecklist") {
          navigate("/checkList");
        } else {
          navigate("/formpage");
        }
      } else if (corrResp.status === "FAILED") {
        SetdisableBtn(false);
        if (corrResp.data.CorrectionExplanationError) {
          const failObj = formData.questions.map((el, idx) => {
            if (el.id === "CorrectionExplanation") {
              el.validateErrMsg = corrResp.data.CorrectionExplanationError;
            }
            return el;
          });
          SetFormData({ ...formData, questions: failObj });
          document.getElementById("CorrectionReasonId").scrollIntoView();
          document.getElementById("CorrectionExplanation").focus();
        } else {
          SetFlag(true);
          SetMsg(
            corrResp.data.ErrorMessage ||
              "An error occurred, please try again later!"
          );
          document.getElementById("scrollToTop").scrollIntoView();
          document.getElementById("scrollToTop").focus();
        }
      } else if (corrResp.status === "SERVERERROR") {
        SetFlag(true);
        SetMsg(corrResp.data);
        SetdisableBtn(false);
        document.getElementById("scrollToTop").scrollIntoView();
        document.getElementById("scrollToTop").focus();
      }
    } else {
      let answerObj = JSON.stringify(answers) === "{}" ? false : answers;
      const dt = await SaveCertForm(
        "PostAppCertification",
        userForm,
        answerObj
      );
      if (dt.status === "SUCCESS") {
        if (dt.data.PageName === "ApplicationCharges") {
          setpageFormUrl("FinalCharge");
          navigate("/finalCharge");
        } else if (dt.data.PageName === "Dashboard") {
          setpageFormUrl("Dashboard");
          navigate("/dashboard");
        } else if (dt.data.PageName === "SubmitCorrection") {
          setpageFormUrl("SubmitCorrection");
          navigate("/submitCorrection");
        } else if (dt.data.PageName === "CorrectionSubmitFailed") {
          SetFlag(true);
          SetMsg(dt.data.ErrorMessage);
          SetdisableBtn(false);
        }
      } else if (urlPage === "CreditDebit") {
        navigate("/dashboard");
      } else {
        navigate("/dashboard");
      }
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      SetCertFlag(false);
      setpageUrl("payment");
      backPageButton.push(userForm);
      if (!backButtonMode) {
        setPageIndex(backPageButton.length - 2);
      }
      setShowModal(true);
      /* Delete If statement*/
      if (urlPage === "CreditDebit") {
        dtjson.find((p) => {
          if (p.PageName === urlPage) {
            const js = formPageFromJSON(p);
            SetTitlePage(js.title);
            SetFormData(js);
          }
        });
      } else if (urlPage === "Correction") {
        const corr = {
          Username: userForm.Username,
          UserId: userForm.UserId,
          AwardYear: userForm.AwardYear,
          ApplicationId: userForm.ApplicationId,
          OrgFormId: userForm.OrgFormId,
          FormId: userForm.FormId,
          PageName: "Correction",
        };
        const cr = await getSaveCert("GetCorrection", corr);
        if (cr.status === "SUCCESS") {
          SetFlag(false);
          setUserForm(cr.data);
          dtjson.find((p) => {
            if (p.PageName === urlPage) {
              const js = formPageFromJSON(p);
              SetTitlePage(js.title);
              let obj = {};
              if (cr.data.CorrectionReasonId) {
                obj.CorrectionReasonId = cr.data.CorrectionReasonId;
              }
              if (cr.data.CorrectionExplanation) {
                obj.CorrectionExplanation = cr.data.CorrectionExplanation;
              }
              setAnswers(obj);
              if (cr.data.AllowCorrectionReasonUpdate === "N") {
                js.questions?.map((el, idx) => {
                  if (el.id === "CorrectionReasonId") {
                    el.disabled = true;
                  }
                  return el;
                });
              }
              js.questions[1].validateErrMsg = "";
              SetFormData(js);
            }
          });
        } else if (cr.status === "FAILED") {
          SetFlag(true);
          SetMsg(cr.data.ErrorMessage);
        } else if (cr.status === "SERVERERROR") {
          SetFlag(true);
          SetMsg(cr.data);
        }
      } else if (urlPage === "CodePayment") {
        const frms = {
          Username: userForm.Username,
          UserId: userForm.UserId,
          AwardYear: userForm.AwardYear,
          ApplicationId: userForm.ApplicationId,
          OrgFormId: userForm.OrgFormId,
          FormId: userForm.FormId,
          ApplicationChargeId: userForm.ApplicationChargeId,
          PageName: "FeePaymentCode",
        };
        const dt = await getSaveCert("GetFeePaymentCodeSetup", frms);
        if (dt.status === "SUCCESS") {
          SetFlag(false);
          SetTitlePage(dt.data.PageHeading);
          setUserForm(dt.data);
          if (dt.data.FPCEntryCount) {
            let ars = [];
            let s = [...Array(parseInt(dt.data.FPCEntryCount)).keys()];
            s.map(() => {
              let obj = { WildCodeCSSCode: "", FeePaymentCode: "" };
              ars.push(obj);
            });
            setAnswersCode(ars);
          }
          SetFormData(dt.data);
        } else if (dt.status === "FAILED") {
          SetFlag(true);
          SetMsg(dt.data.ErrorMessage);
        } else if (dt.status === "SERVERERROR") {
          SetFlag(true);
          SetMsg(dt.data);
        }
        setShowModal(false);
      } else {
        const frm = {
          Username: userForm.Username,
          UserId: userForm.UserId,
          AwardYear: userForm.AwardYear,
          ApplicationId: userForm.ApplicationId,
          OrgFormId: userForm.OrgFormId,
          FormId: userForm.FormId,
          PageName: "ApplicationCertification",
        };
        SetCertFlag(true);
        const dts = await getSaveCert("GetAppCertification", frm);
        if (dts.status === "SUCCESS") {
          SetFlag(false);
          setUserForm(dts.data);
          let jsCert = formPageFromJSON(dts.data);
          SetTitlePage(jsCert.title);
          SetFormData(jsCert);
        } else if (dts.status === "FAILED") {
          SetFlag(true);
          SetMsg(dts.data.ErrorMessage);
        } else if (dts.status === "SERVERERROR") {
          SetFlag(true);
          SetMsg(dts.data);
        }
      }
      setShowModal(false);
    };
    fetchData();
    document.getElementById("scrollToTop").scrollIntoView();
    document.getElementById("scrollToTop").focus();
  }, [urlPage]);

  const goToNextPage = async (url) => {
    navigate("/checkList");
  };
  const goBackToPage = async () => {
    setBackButtonMode(true);
    const goBack = backPageButton[pageIndex];
    setPageIndex(pageIndex - 1);
    setUserForm(goBack);
    setpageFormUrl(goBack.PageName);
    // if (certFlag) {
    //   navigate("/appReviewIntro")
    // } else
    if (urlPage === "Correction") {
      navigate("/dashboard");
    } else if (
      // goBack.PageName === "AcademicInfo3" ||
      goBack.PageName === "AcademicInfo3a"
    ) {
      navigate("/school");
    } else if (goBack.PageName === "AcademicInfo4") {
      navigate("/program");
    } else if (
      goBack.PageName === "Correction" ||
      goBack.PageName === "Certification"
    ) {
      navigate("/payment");
    } else if (goBack.PageName === "AppCheckList") {
      navigate("/checkList");
    } else if (
      goBack.PageName === "FinalCharge" ||
      goBack.PageName === "ApplicationCharge"
    ) {
      navigate("/finalCharge");
    } else if (goBack.PageName === "ApplicationReview") {
      navigate("/appReview");
    } else if (goBack.PageName === "ApplicationReviewIntro") {
      navigate("/appReviewIntro");
    } else if (goBack.Multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={titlePage}
      />
      <div className="mx-auto space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple mt-12 md:mt-6">
        {showModal ? <ProcessingModal /> : null}
        {flag && <ValidationError message={msg} />}
        {showWarning ? (
          <div className="space-y-5">
            <div className="md:text-xl font-bold text-lg">Warning</div>
            <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5">
              <p>
                If you are attempting to update your CSS Profile to add parent
                financial information, please contact Customer Service to begin
                the process. Once Customer Service has confirmed your
                application is ready you can return to this page to proceed with
                your correction.
              </p>
              <p>
                Please note that adding parent financial information will
                require you to answer additional questions on your CSS Profile
                Application.
              </p>
            </div>
            <div className="space-x-2">
              <Button
                onClick={() => SetShowWarning(false)}
                className="w-full md:w-fit"
                // id={`GoBackBtn1`}
              >
                Close
              </Button>
              <Button
                onClick={() => {
                  SetShowWarning(false);
                  window.open(
                    "https://cssprofile.collegeboard.org/contact-us",
                    "_blank"
                  );
                }}
                variant="white"
                className="w-full md:w-fit"
              >
                Contact Us
              </Button>
            </div>
          </div>
        ) : (
          <>
            <form
              onSubmit={(event) => onFormSubmition(event)}
              className="space-y-5"
            >
              {urlPage === "Correction" ? null : (
                <p className="md:text-sm text-md font-bold" tabindex="-1">
                  <a
                    href="#nolinkID"
                    className="aStyle"
                    onClick={() => goToNextPage("ApplicationChecklist")}
                    id={`GoBackBtn1`}
                  >
                    ←Sections
                  </a>
                  {` / ${titlePage}`}
                </p>
              )}
              <hr />
              {formData.heading?.textContent && (
                <div className="md:text-xl text-lg space-y-2">
                  {parse(formData.heading.textContent)}
                  {urlPage === "CreditDebit" ? (
                    <div>
                      <p>
                        Total Amount Due: <b>${userForm.BalanceDueAmount}</b>
                      </p>
                    </div>
                  ) : null}
                </div>
              )}
              <ValidationError message={amountMsg} />
              {urlPage !== "CodePayment" ? (
                formData?.questions?.map((question) =>
                  question.hideElem ? null : (
                    <Question
                      key={question.id}
                      {...question}
                      label={question.textContent}
                      dicts={formData.dicts}
                      error={question.validateErrMsg}
                      value={answers ? answers[question.id] : ""}
                      onChange={(value) =>
                        setAnswer(
                          question.id,
                          value,
                          question.conditionalRequireQuestions,
                          question.conditionalDisableQuestions
                        )
                      }
                      onClick={(value) => setAnswer(question.id, value)}
                    />
                  )
                )
              ) : (
                <div className="space-y-5">
                  {formData.PageText ? (
                    <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50 p-5">
                      {parse(formData.PageText)}
                    </div>
                  ) : null}
                  {formData.FPCView === "HHA" ? (
                    answersCode.map((el, i) => (
                      <div
                        key={i}
                        className="space-y-3 mx-auto space-y-5 rounded-sm border-2 md:shadow-md md:shadow-gray-500 bg-white p-3"
                      >
                        <Label htmlFor={"Code" + i}>Fee Payment Code</Label>
                        <TextInput
                          id={"Code" + i}
                          error={el.validateErrMsg}
                          onChange={(event) => setAnswerCode(i, event, "code")}
                        />
                        <Label htmlFor={"College" + i}>College/Program</Label>
                        <SelectInput
                          id={"College" + i}
                          options={formData.FWFeePaymentCodeOptionList}
                          onChange={(event) =>
                            setAnswerCode(i, event, "college")
                          }
                        />
                        {el.validateErrMsg && (
                          <ValidationError message={el.validateErrMsg} />
                        )}
                      </div>
                    ))
                  ) : (
                    <div>
                      <Label htmlFor={"9453"}>Fee Payment Code</Label>
                      <TextInput
                        error={formData.error}
                        id={"9453"}
                        onChange={(event) => setAnswerCodeHHB("9453", event)}
                      />
                      {formData.error && (
                        <ValidationError message={formData.error} />
                      )}
                    </div>
                  )}
                  {formData.PageFooterText ? (
                    <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5">
                      {parse(formData.PageFooterText)}
                    </div>
                  ) : null}
                </div>
              )}
              {formData && (
                <NextPageButton
                  hasQuestions={formData?.questions?.length !== 0}
                  urlPage={urlPage}
                  disableBtn={disableBtn}
                />
              )}
            </form>
            <hr />
            {pageIndex > 0 ? (
              <a
                href="#nolinkID"
                className="aStyle md:text-sm text-md grid justify-items-end"
                onClick={() => goBackToPage()}
              >
                ←Go Back
              </a>
            ) : null}
          </>
        )}
      </div>
    </Layout>
  );
};
function NextPageButton({ hasQuestions, urlPage, disableBtn }) {
  const label = hasQuestions
    ? urlPage === "CreditDebit"
      ? "Submit Payment"
      : urlPage === "CodePayment"
      ? "Submit"
      : "Save and Continue"
    : "Continue";
  return (
    <Button className="w-full md:w-fit" disabled={disableBtn}>
      {label}
    </Button>
  );
}
export default WithLocation(Payment);
