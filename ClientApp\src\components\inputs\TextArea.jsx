import React from "react";
import { borderStyles, inputStyles, focusStyles } from "../shared/inputs";

export function TextArea({
  error,
  onChange,
  minLength,
  maxLength,
  ...textInputProps
}) {
  const preventUnwantedCharacters = (event) => {
    const allawedCharacters =
      "abcdefghijklmnopqrstuvwxyz1234567890!#$'()*,+-.:;=?@^_`{|}~%[]&<>";
    let val = event.key.toLowerCase();
    if (
      !allawedCharacters.includes(val) &&
      val !== "backspace" &&
      val !== "delete" &&
      val !== " " &&
      val !== "tab"
    ) {
      event.preventDefault();
    }
  };
  return (
    <textarea
      className={`${inputStyles} ${focusStyles} ${borderStyles(error)}`}
      rows="5"
      cols="33"
      onChange={(event) => onChange(event.target.value)}
      minLength={minLength}
      maxLength={maxLength}
      onKeyDown={preventUnwantedCharacters}
      {...textInputProps}
    />
  );
}
