import React from "react"
import { useStaticQuery, graphql } from "gatsby"
import parse from "html-react-parser"
import { But<PERSON> } from "./Button"

const Congrats = ({ heading, text, list, onClick }) => {
  const data = useStaticQuery(graphql`
    query {
      star: file(relativePath: { eq: "star.svg" }) {
        name
        publicURL
      }
    }
  `)
  return (
    <div className="space-y-5">
      <div className="mt-3 p-4">
        <div className=" text-center">
          <img
            src={process.env.IMG_URL + data.star.publicURL}
            alt=""
            height={62}
            width={62}
            className="inline"
          />
        </div>
      </div>
      {/* <Image src={"/congrats.svg"} alt="" height={62} width={62} /> */}
      <div className="space-y-5">
        <p className="text-center md:text-xl text-lg font-bold text-sky-700">
          {heading}
        </p>
        <p className="p-5">{parse(text)}</p>
      </div>
      {list.map((el, ind) => (
        <div key={el.Display} className="space-y-5 pl-5">
          <Button
            type="button"
            variant={ind === 0 ? "primary" : "secondary"}
            onClick={() => onClick("review", el.Value)}
            className="w-full pl-5 md:w-fit"
          >
            {el.Display}
          </Button>
        </div>
      ))}
    </div>
  )
}
export default Congrats
