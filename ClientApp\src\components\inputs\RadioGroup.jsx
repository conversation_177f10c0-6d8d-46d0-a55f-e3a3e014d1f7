import React from "react"
import RadioButton from "./RadioButton"

export function RadioGroup({
  options,
  id,
  onChange,
  error,
  required,
  disabled,
  value,
}) {
  return (
    <div id={id} className="space-y-5">
      {options.map(option => {
        return (
          <RadioButton
            name={id}
            key={option.Value}
            label={option.Display}
            value={option.Value}
            checked={value === option.Value}
            onChange={({ value }) => {
              onChange(value)
            }}
            {...{ error, required, disabled }}
          />
        )
      })}
    </div>
  )
}
