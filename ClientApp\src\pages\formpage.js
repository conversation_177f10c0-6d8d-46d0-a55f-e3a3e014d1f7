import React, { useContext, useEffect, useState } from "react";
import { navigate } from "gatsby";
import {
  formPageFromJSON,
  saveFormPage,
  requiredHiddenFields,
} from "../components/NewComp";
import Layout from "../components/layout";
import axios from "axios";
import ExpandableHint from "../components/ExpandableHint";
import Hint from "../components/Hint";
import { Button } from "../components/Button";
import Question from "../components/Question";
import parse from "html-react-parser";
import Helmet from "react-helmet";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";

const FormPage = ({ ttl, content }) => {
  const {
    setUserLogedIn,
    setpageFormUrl,
    userForm,
    isUserLogedIn,
    formURL,
    setUserForm,
    setpageUrl,
    backPageButton,
    setBackPageButton,
    pageIndex,
    setPageIndex,
    backButtonMode,
    setBackButtonMode,
    dollarFormat,
    moneyObj,
    SSNValue,
  } = useUserStore((state) => ({
    setUserLogedIn: state.setUserLogedIn,
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    userForm: state.userForm,
    formURL: state.formURL,
    isUserLogedIn: state.isUserLogedIn,
    setpageUrl: state.setpageUrl,
    backPageButton: state.backPageButton,
    setBackPageButton: state.setBackPageButton,
    pageIndex: state.pageIndex,
    setPageIndex: state.setPageIndex,
    backButtonMode: state.backButtonMode,
    setBackButtonMode: state.setBackButtonMode,
    dollarFormat: state.dollarFormat,
    moneyObj: state.moneyObj,
    SSNValue: state.SSNValue,
  }));

  const [flag, SetFlag] = useState(false);
  const [isYesNoPage, SetIsYesNoPage] = useState(false);
  const [urlPage, SetUrlPage] = useState(formURL);
  const [msg, SetMsg] = useState("");
  const [formData, SetFormData] = useState({});
  const [answers, setAnswers] = useState();
  const [showModal, setShowModal] = React.useState(false);
  const [showConfirm, setShowConfirm] = React.useState("fake");
  const [openConfirm, setOpenConfirm] = React.useState(false);
  const [disableBtn, SetdisableBtn] = useState(false);
  const [showMsgOnTop, SetShowMsgOnTop] = useState(false);
  const setAnswer = (questionId, answer, req, dis, ssnVal) => {
    if (questionId === "f_stu_ssn") answer = SSNValue.replace(/[^0-9]/g, "");
    setAnswers({ ...answers, [questionId]: answer });
    if (req && req.length > 0) {
      req.map((r) => {
        let arr = r.condReqParVal.split(";");
        let cond = r.condOperator;
        formData.questions.map((q) => {
          if (r.requiredQuestionId === q.id) {
            if (cond === "=") {
              if (arr.includes(answer)) {
                q.required = "Y";
              } else {
                q.required = "N";
              }
            } else if (cond === ">") {
              if (arr.includes(answer)) {
                q.required = "Y";
              } else {
                q.required = "N";
              }
            }
          }
        });
      });
    }

    if (dis && dis.length > 0) {
      dis.map((d) => {
        let empty = "";
        let notin = "";
        if (d.condDisableParValEQ) {
          empty = d.condDisableParValEQ.split(";");
        }
        if (d.condDisableParValNOTEQ) {
          notin = d.condDisableParValNOTEQ.split(";");
        }
        formData?.questions.map((o) => {
          if (d.disableQuestionId === o.id) {
            if (empty && empty.includes(answer)) {
              o.hideElem = true;
            } else if (notin && !notin.includes(answer)) {
              o.hideElem = true;
            } else {
              o.hideElem = false;
            }
          }
        });
      });
    }
  };
  const [yesNoErr, SetYesNoErr] = useState(false);
  const onFormSubmition = async (event) => {
    event.preventDefault();
    setOpenConfirm(false);
    if (
      (urlPage === "StudentInfo3" ||
        (urlPage === "StudentInfo4" &&
          answers.f_stu_legal_dependents === "Y")) &&
      showConfirm === "fake"
    ) {
      setOpenConfirm(true);
      setShowConfirm("confirm");
      return;
    } else if (
      urlPage === "ConfirmDemographics" &&
      showMsgOnTop &&
      showConfirm === "fake"
    ) {
      setOpenConfirm(true);
      setShowConfirm("confirm");
      document.getElementById("scrollToTop").scrollIntoView();
      document.getElementById("GoBackBtn1").focus();
      return;
    } else if (formURL === "HHBMessage1") {
      navigate("/checkList");
    }
    SetdisableBtn(true);
    if (isYesNoPage && answers.yes_no_button === "") {
      SetYesNoErr(
        "This field is required. Please select a response to continue."
      );
      return;
    }
    SetYesNoErr(null);
    setBackButtonMode(false);
    let answObj = answers;
    if (answObj.hasOwnProperty("f_stu_ssn") && answObj.f_stu_ssn !== "") {
      answObj = {
        ...answers,
        f_stu_ssn: SSNValue !== "" ? SSNValue.replace(/[^0-9]/g, "") : "",
      };
    } else if (answObj.hasOwnProperty("P1SSN") && answObj.P1SSN !== "") {
      answObj = {
        ...answers,
        P1SSN: SSNValue !== "" ? SSNValue.replace(/[^0-9]/g, "") : "",
      };
    } else if (answObj.hasOwnProperty("P2SSN") && answObj.P2SSN !== "") {
      answObj = {
        ...answers,
        P2SSN: SSNValue !== "" ? SSNValue.replace(/[^0-9]/g, "") : "",
      };
    }
    const answersObj = {};
    formData?.questions?.map((question) => {
      for (const [key, value] of Object.entries(answObj)) {
        if (key === question.id && question.format === "money") {
          let newVal = value.replace(/ /g, "");
          answersObj[key] = newVal;
        } else if (key === question.id && question.format !== "money") {
          answersObj[key] = value;
        }
      }
    });

    let answerObj = JSON.stringify(answersObj) === "{}" ? false : answersObj;
    const res = await saveFormPage(userForm, answerObj);

    if (res.status === "SUCCESS") {
      SetdisableBtn(false);
      SetFlag(false);
      setUserForm(res.data);
      setpageFormUrl(res.data.PageName);
      setShowConfirm("fake");
      SetFormData({});
      if (
        res.data.PageName === "AcademicInfo3a"
        // || res.data.PageName === "AcademicInfo3"
      ) {
        navigate("/school");
      } else if (res.data.PageName === "AcademicInfo4") {
        navigate("/program");
      } else if (res.data.PageName === "ApplicationChecklist") {
        navigate("/checkList");
      } else if (res.data.Multiple === "Y") {
        navigate("/multiple");
      } else {
        SetUrlPage(res.data.PageName);
      }
    } else if (res.status === "FAILED") {
      SetdisableBtn(false);
      if (res.data.ErrorMessage) {
        SetFlag(true);
        SetMsg(res.data.ErrorMessage);
      } else {
        if (res.data.QuestionErrorList.length > 0) {
          ValErrors(res.data.QuestionErrorList);
        }
      }
    } else if (res.status === "SERVERERROR") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(res.data);
    }
  };
  const ValErrors = (flObj) => {
    const failObj = formData.questions.map((el, idx) => {
      flObj.map((elem, ind) => {
        if (elem.Id === el.id) {
          el.validateErrMsg = elem.ErrorMsg;
          //el.required = "Y"
        }
      });
      return el;
    });
    SetFormData({ ...formData, questions: failObj });
  };
  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      setpageUrl("formpage");
      setShowModal(true);
      SetShowMsgOnTop(false);
      setShowConfirm("fake");
      const formUser = {
        ...userForm,
        PageName: urlPage,
      };
      const bodyFormData = new FormData();
      let urlDirect = process.env.API_URL + "GetFormPageItems";
      bodyFormData.append("sbr", JSON.stringify(formUser));
      backPageButton.push(formUser);
      if (!backButtonMode) {
        setPageIndex(backPageButton.length - 2);
      }
      await axios({
        method: "post",
        url: urlDirect,
        data: bodyFormData,
        headers: { "Content-Type": "multipart/form-data" },
      })
        .then((res) => {
          setShowModal(false);
          if (res.status === 200) {
            const js = formPageFromJSON(res.data);
            if (
              js.questionAnswers.MatchDataConfirmed === "Y" &&
              urlPage === "ConfirmDemographics"
            ) {
              SetShowMsgOnTop(true);
            }
            setAnswers(js.questionAnswers);
            if (js.questions.length > 0) {
              js.questions.map((question) => {
                if (question.dict === "YesNo_YN") {
                  question.fieldType = "yesnobutton";
                  if (question.required) SetIsYesNoPage(true);
                }
              });
              requiredHiddenFields(js);
            }
            SetFormData(js);
            SetFlag(false);
          } else if (res.data.returnMessage === "SERVERERROR") {
            SetFlag(true);
            SetMsg("Server error has occured.");
          } else if (res.data.ReturnMessage === "FAILED") {
            SetFlag(true);
            SetMsg("No data found.");
          }
        })
        .catch((err) => {
          SetMsg("Server error has occured.");
          SetFlag(true);
          setShowModal(false);
        });
    };
    fetchData();
    document.getElementById("scrollToTop").scrollIntoView();
    document.getElementById("GoBackBtn1").focus();
  }, [urlPage]);

  const goToNextPage = async (url) => {
    navigate("/checkList");
  };
  const goBackToPage = async () => {
    setBackButtonMode(true);
    const goBack = backPageButton[pageIndex];
    setPageIndex(pageIndex - 1);
    setUserForm(goBack);
    setpageFormUrl(goBack.PageName);
    if (
      // goBack.PageName === "AcademicInfo3" ||
      goBack.PageName === "AcademicInfo3a"
    ) {
      navigate("/school");
    } else if (goBack.PageName === "AcademicInfo4") {
      navigate("/program");
    } else if (goBack.PageName === "Correction") {
      navigate("/payment");
    } else if (goBack.PageName === "AppCheckList") {
      navigate("/checkList");
    } else if (goBack.PageName === "DataChecks") {
      navigate("/dataChecks");
    } else if (goBack.PageName === "ApplicationReview") {
      navigate("/appReview");
    } else if (goBack.Multiple === "Y") {
      navigate("/multiple");
    } else {
      SetUrlPage(goBack.PageName);
    }
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={formData.title}
      />
      <div className="mx-auto space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple mt-12 md:mt-6">
        {showModal ? <ProcessingModal /> : null}
        {flag && <ValidationError message={msg} />}
        {yesNoErr && <ValidationError message={yesNoErr} />}
        <form
          onSubmit={(event) => onFormSubmition(event)}
          className="space-y-5"
        >
          <div className="font-bold md:text-sm text-md">
            <div>
              <a
                href="#nolinkID"
                className="aStyle"
                onClick={() => goToNextPage("ApplicationChecklist")}
                id="GoBackBtn1"
              >
                ←Sections
              </a>{" "}
              <span>{` / ${formData.title}`}</span>
            </div>
          </div>
          <hr />
          {formData.headingText ? (
            <h2 className=" md:text-lg text-xl font-bold">
              {parse(formData.headingText)}
            </h2>
          ) : null}
          {formData.heading?.textContent && urlPage !== "StudentInfoCit1" ? (
            <div className=" md:text-xl text-lg">
              {parse(formData.heading.textContent)}
            </div>
          ) : null}
          {formData.heading?.textContent && urlPage === "StudentInfoCit1" ? (
            <div className="space-y-3 rounded-md border-2 border-solid border-green-600 bg-green-50 p-5 text-base">
              {parse(formData.heading.textContent)}
            </div>
          ) : null}
          {openConfirm && formURL === "ConfirmDemographics" && showMsgOnTop ? (
            <div
              className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50 p-5"
              id="ShowMsgOnTo"
            >
              <p>
                <strong>{"Warning: "}</strong>Do not change student information
                within these sections unless you incorrectly entered your
                information. Contact customer service prior to updating the
                student information.
              </p>
            </div>
          ) : null}
          {formData.heading?.helpContent ? (
            formData.heading?.helpStyle === "Expandable Hint" ? (
              <ExpandableHint
                title={formData.heading?.helpTitle}
                content={parse(formData.heading?.helpContent)}
              />
            ) : formData.heading?.helpStyle === "Hint" ? (
              <Hint description={formData.heading?.helpContent} />
            ) : (
              <Hint description={formData.heading?.helpContent} notice="true" />
            )
          ) : null}

          {formData?.questions?.map((question) =>
            question.hideElem ? null : (
              <Question
                key={question.id}
                {...question}
                label={question.textContent}
                dicts={formData.dicts}
                error={question.validateErrMsg}
                value={answers ? answers[question.id] : ""}
                onChange={(value, prevSSN) =>
                  setAnswer(
                    question.id,
                    value,
                    question.conditionalRequireQuestions,
                    question.conditionalDisableQuestions,
                    prevSSN
                  )
                }
                onClick={(value) =>
                  setAnswer(
                    question.id,
                    value,
                    question.conditionalRequireQuestions,
                    question.conditionalDisableQuestions
                  )
                }
              />
            )
          )}
          {openConfirm ? (
            <div className="space-y-3">
              {formURL === "ConfirmDemographics" ? null : (
                <div className="space-y-3 rounded-md border-2 border-solid border-red-600 bg-red-50  p-5 text-base">
                  <ValidationError
                    message={
                      urlPage === "StudentInfo3"
                        ? `Please confirm that ${
                            answers?.f_stu_dob ? answers.f_stu_dob : null
                          } is the student's date of birth.`
                        : `You answered “Yes” that the student has their own child(ren) or other legal dependents. Please confirm your answer is correct before proceeding.`
                    }
                  />
                </div>
              )}
              <div>
                <Button
                  type="button"
                  className="w-full md:w-fit"
                  onClick={(el) => {
                    onFormSubmition(el);
                  }}
                >
                  Confirm
                </Button>
                <Button
                  type="button"
                  className="w-full md:w-fit ml-2"
                  onClick={(el) => {
                    setShowConfirm("fake");
                    setOpenConfirm(false);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            formData && (
              <NextPageButton
                disableBtn={disableBtn}
                hasQuestions={formData?.questions?.length !== 0}
                page={formURL}
              />
            )
          )}
        </form>
        <hr />
        {pageIndex > 0 ? (
          <a
            href="#nolinkID"
            className="aStyle md:text-sm text-md grid justify-items-end"
            onClick={() => goBackToPage()}
          >
            ←Go Back
          </a>
        ) : null}
      </div>
    </Layout>
  );
};
function NextPageButton({
  hasQuestions,
  checkForReviewPage,
  disableBtn,
  page,
}) {
  //HHBmessage1
  const label = hasQuestions
    ? "Save and Continue"
    : page === "HHBMessage1"
    ? "Return to Application Checklist"
    : "Continue";
  return (
    <Button type="submit" className="w-full md:w-fit" disabled={disableBtn}>
      {label}
    </Button>
  );
}
export default WithLocation(FormPage);
