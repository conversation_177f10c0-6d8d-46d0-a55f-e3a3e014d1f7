{"name": "@types/responselike", "version": "1.0.0", "description": "TypeScript definitions for responselike", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "38ee8db1511cdb4a9133ff67b8bc16901de733aa4dc1efffdb5064b7daaa3f21", "typeScriptVersion": "2.0"}