import React, { useEffect, useState } from "react";
import { navigate } from "gatsby";
import parse from "html-react-parser";
import { States } from "../fakeDB/state";
import Layout from "../components/layout";
import Helmet from "react-helmet";
import { Label } from "../components/inputs/Label";
import { SelectInput } from "../components/inputs/SelectInput";
import { TextInput } from "../components/inputs/TextInput";
import { Button } from "../components/Button";
import axios from "axios";
import { ProcessingModal } from "../components/processingModal";
import { saveFormPage } from "../components/NewComp";
import { ValidationError } from "../components/ValidationError";
import Hint from "../components/Hint";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";

const Schools = () => {
  const {
    setpageFormUrl,
    userForm,
    isUserLogedIn,
    setUserForm,
    setpageUrl,
    backPageButton,
    pageIndex,
    setPageIndex,
    backButtonMode,
    setBackButtonMode,
  } = useUserStore((state) => ({
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    userForm: state.userForm,
    isUserLogedIn: state.isUserLogedIn,
    setpageUrl: state.setpageUrl,
    backPageButton: state.backPageButton,
    setBackButtonMode: state.setBackButtonMode,
    setBackPageButton: state.setBackPageButton,
    pageIndex: state.pageIndex,
    setPageIndex: state.setPageIndex,
    backButtonMode: state.backButtonMode,
  }));

  const [isChecked, setIsChecked] = useState("COLSearch");
  const [selId, setSelId] = useState(true);
  const [stateSel, setStateSel] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [dataObj, setDataObj] = useState([]);
  const [schoolObj, setSchoolObj] = useState([]);
  const [flag, SetFlag] = useState(false);
  const [msg, SetMsg] = useState("");
  const [recordCount, SetRecordCount] = useState(0);
  const [disableBtn, SetdisableBtn] = useState(false);
  const [awardYear, SetAwardYear] = useState("");

  const Component = ({ name, id, children, isRequired }) => {
    return (
      <>
        <Label required={isRequired} for={id}>
          {name}
        </Label>
        {children}
      </>
    );
  };
  const onFormSubmition = async (event) => {
    event.preventDefault();
    SetdisableBtn(true);
    setShowModal(true);
    setSelId(false);
    setBackButtonMode(false);
    setDataObj([]);
    setSchoolName(false);
    let url;
    if (isChecked === "COLSearch") {
      //SearchFunction
      url = `disearch`;
    } else {
      url = `aisearch`;
    }
    url =
      url +
      `&schoolName=${
        document.getElementById("schoolName").value
      }&stateCode=${stateSel}`;
    await axios({
      method: "get",
      url: process.env.API_SCHOOL + url,
    })
      .then((res) => {
        setShowModal(false);
        if (res.status === 200) {
          SetdisableBtn(false);
          SetRecordCount(res.data.hits.total);
          setDataObj(res.data.hits.hits);
        } else {
          SetdisableBtn(false);
          console.log(res.data);
        }
      })
      .catch((err) => {
        SetdisableBtn(false);
        setShowModal(false);
        return;
      });
  };
  const bldCard = (obj) => {
    const retObj = (
      <div key={obj.org_id}>
        <div
          className={`flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row`}
        >
          <div className="font-bold">{obj.org_full_name}</div>
          <div>{`${obj.org_city}, ${obj.org_state_cd}`}</div>
        </div>
        <div className={`space-y-2 p-3`}></div>
        <a
          href="#nolinkID"
          className="cursor-pointer text-center font-bold text-sky-700 underline"
          onClick={() => AddCollege(obj.org_id, obj.org_full_name, true)}
        >
          Select {obj.org_full_name}
        </a>
      </div>
    );
    return retObj;
  };
  const [orgSel, setOrgSel] = useState({});
  const [schoolName, setSchoolName] = useState(false);
  const AddCollege = (code, name, flag) => {
    setSchoolName(true);
    setSelId(true);
    SetRecordCount(0);
    let o = { code: code, name: name };
    setOrgSel(o);
    if (flag) SubmitData(code, name);
  };
  const SubmitData = async (cde, nme) => {
    let code = "";
    let name = "";
    if (cde) {
      code = cde;
      name = nme;
    } else {
      code = orgSel.code;
      name = orgSel.name;
    }
    SetdisableBtn(true);
    setShowModal(true);
    let nameSchool = name.replace("&", "And");
    let o =
      isChecked === "COLSearch"
        ? {
            C1Ceeb: code,
            q_stu_pr_yr_sch_name: nameSchool,
          }
        : { HSID: code, HSName: nameSchool };
    const res = await saveFormPage(userForm, o);
    if (res.status === "SUCCESS") {
      SetdisableBtn(false);
      SetFlag(false);
      setUserForm(res.data);
      setpageFormUrl(res.data.PageName);
      if (res.data.PageName === "AcademicInfo4") {
        navigate("/program");
      }
      if (res.data.Multiple === "Y") {
        navigate("/multiple");
      } else {
        navigate("/formpage");
      }
    } else if (res.status === "FAILED") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(res.data.QuestionErrorList);
    } else if (res.status === "SERVERERROR") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(res.data);
    }
    document.getElementById("scrollToTop").scrollIntoView();
    document.getElementById("GoBackBtn1").focus();
    setShowModal(false);
  };

  const goToNextPage = async (url) => {
    setpageFormUrl(url);
    navigate("/checkList");
  };
  const Section = ({ title, children }) => {
    return (
      <section className="flex w-full flex-col gap-2">
        <h2 id={title} className="group md:text-xl text-lg font-bold">
          {title}
        </h2>
        {children}
      </section>
    );
  };
  const tempCodes = [];
  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      setpageUrl("school");
      const formUser = userForm;
      const bodyFormData = new FormData();
      bodyFormData.append("sbr", JSON.stringify(formUser));
      backPageButton.push(formUser);
      if (!backButtonMode) {
        setPageIndex(backPageButton.length - 2);
      }
      await axios({
        method: "post",
        url: process.env.API_URL + "GetFormPageItems",
        data: bodyFormData,
        headers: { "Content-Type": "multipart/form-data" },
      })
        .then((res) => {
          setShowModal(false);
          if (res.status === 200) {
            SetAwardYear(res.data.FWFormItemList[0].textContent);
            setIsChecked(res.data.SearchFunction);
            setSchoolObj(res.data);
            res.data.FWFormItemList.map((el) => {
              if (el.itemType === "question") {
                if (res.data.SearchFunction === "COLSearch") {
                  if (el.id === "C1Ceeb" && el.questionValue) {
                    setSchoolName(el.questionValue);
                    setOrgSel({ ...orgSel, ["code"]: el.questionValue });
                    tempCodes.push(el.questionValue);
                  } else if (
                    el.id === "q_stu_pr_yr_sch_name" &&
                    el.questionValue
                  ) {
                    setSchoolName(el.questionValue);
                    setOrgSel({ ...orgSel, ["name"]: el.questionValue });
                    tempCodes.push(el.questionValue);
                  }
                } else if (res.data.SearchFunction === "HSSearch") {
                  if (el.id === "HSID" && el.questionValue) {
                    // setSchoolName(el.questionValue)
                    // setOrgSel({ ...orgSel, ["code"]: el.questionValue })
                    tempCodes.push(el.questionValue);
                  } else if (el.id === "HSName" && el.questionValue) {
                    // setSchoolName(el.questionValue)
                    // setOrgSel({ ...orgSel, ["name"]: el.questionValue })
                    tempCodes.push(el.questionValue);
                  }
                }
              }
            });
            if (tempCodes.length > 0)
              AddCollege(tempCodes[0], tempCodes[1], false);
          } else if (res.data.returnMessage === "SERVERERROR") {
            SetFlag(true);
            SetMsg("Server error has occured.");
            return;
          } else if (res.data.ReturnMessage === "FAILED") {
            SetFlag(true);
            SetMsg("No data found.");
            return;
          }
        })
        .catch((err) => {
          SetMsg("Server error has occured.");
          setShowModal(false);
          return;
        });
    };
    fetchData();
  }, []);
  const goBackToPage = async () => {
    setBackButtonMode(true);
    const goBack = backPageButton[pageIndex];
    setPageIndex(pageIndex - 1);
    setUserForm(goBack);
    setpageFormUrl(goBack.PageName);
    if (
      goBack.PageName === "AcademicInfo3"
      // || goBack.PageName === "AcademicInfo3a"
    ) {
      navigate("/school");
    } else if (goBack.PageName === "AcademicInfo4") {
      navigate("/program");
    } else if (goBack.PageName === "Correction") {
      navigate("/payment");
    } else if (goBack.PageName === "AppCheckList") {
      navigate("/checkList");
    } else if (goBack.PageName === "ApplicationReview") {
      navigate("/appReview");
    } else if (goBack.PageName === "DataChecks") {
      navigate("/dataChecks");
    } else if (goBack.Multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={"School Selection"}
      />
      <main className="mx-auto space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple">
        {showModal ? <ProcessingModal /> : null}
        {/* {flag && <ValidationError message={parse(msg)} />} */}
        {msg ? (
          Array.isArray(msg) ? (
            msg.map((err, idx) => (
              <ValidationError key={idx} message={parse(err.ErrorMsg)} />
            ))
          ) : (
            <ValidationError message={parse(msg)} />
          )
        ) : null}
        <div className="font-bold md:text-sm text-md">
          <div>
            <a
              href="#nolinkID"
              id="GoBackBtn1"
              className="cursor-pointer text-sky-700 underline"
              onClick={() => goToNextPage("ApplicationChecklist")}
            >
              ←Sections
            </a>{" "}
            <span>{` / ${schoolObj.PageHeading}`}</span>
          </div>
        </div>
        <hr />
        {selId ? (
          <div className="max-w-md">
            <form onSubmit={(event) => onFormSubmition(event, true)}>
              <Section title={`${schoolObj.PageHeading}`}>
                <Hint description={awardYear} />
                <Component name="State" id={"State"} isRequired={true}>
                  <SelectInput
                    name="State"
                    id="State"
                    required
                    value={stateSel}
                    onChange={(ev) => setStateSel(ev)}
                    options={States}
                  />
                </Component>

                <Component name={`School Name`} id={"schoolName"}>
                  <Hint
                    description={
                      "Please enter the full name of your college or university."
                    }
                  />
                  <TextInput
                    type="text"
                    id={"schoolName"}
                    onChange={(event) => console.log(event)}
                  />
                </Component>

                {/* <Hint
                  description={`Please enter the high school name.
            You may need to enter just a part of the name to ensure a match`}
                /> */}
                <Button disabled={disableBtn} className="w-full md:w-fit">
                  Search
                </Button>
              </Section>
            </form>
          </div>
        ) : null}

        {schoolName && selId ? (
          <Section
            title={`Selected ${
              isChecked === "COLSearch" ? "College" : "High School"
            }`}
          >
            <div className={` max-w-xl space-y-5 rounded-md border-2 p-3`}>
              <div
                className={`flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row`}
              >
                <span className="text-center font-bold text-program-core-higher-ed">
                  {orgSel.name}
                </span>
              </div>
            </div>
            <Button
              type="button"
              onClick={() => SubmitData()}
              className="w-full md:w-fit"
              disabled={disableBtn}
            >
              Save and Continue
            </Button>
          </Section>
        ) : null}

        {!selId ? (
          <Section title="School List">
            {recordCount > 100 ? (
              <ValidationError
                message={`Your search result returns more than 100 schools. Please add additional search criteria to narrow down your search result.`}
              />
            ) : null}
            <div
              key={`000004`}
              className={` max-w-xl space-y-5 rounded-md border-2 p-3`}
            >
              <div key={"NotListed"}>
                <div
                  className={`flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row`}
                >
                  <div className="font-bold">{`Not Listed`}</div>
                  <div>{``}</div>
                </div>
                <div className={`space-y-2 p-3`}></div>
                <a
                  href="#nolinkID"
                  className="cursor-pointer text-center font-bold text-sky-700 underline"
                  onClick={() => AddCollege(`000004`, "Not Listed", true)}
                >
                  Select {"Not Listed"}
                </a>
              </div>
            </div>
            {isChecked === "HSSearch" ? (
              <div
                key={`970000`}
                className={` max-w-xl space-y-5 rounded-md border-2 p-3`}
              >
                <div key={"homeschool"}>
                  <div
                    className={`flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row`}
                  >
                    <div className="font-bold">{`Home School`}</div>
                    <div>{``}</div>
                  </div>
                  <div className={`space-y-2 p-3`}></div>
                  <a
                    href="#nolinkID"
                    className="cursor-pointer text-center font-bold text-sky-700 underline"
                    onClick={() => AddCollege(`970000`, "Home School", true)}
                  >
                    Select {"Home School"}
                  </a>
                </div>
              </div>
            ) : null}
            {dataObj.map((el, idx) => {
              let obj = bldCard(el._source);
              return (
                <div
                  key={idx}
                  className={` max-w-xl space-y-5 rounded-md border-2 p-3`}
                >
                  {obj}
                </div>
              );
            })}
          </Section>
        ) : null}
        <hr />
        {pageIndex > 0 ? (
          <a
            href="#nolinkID"
            className="cursor-pointer text-sky-700 underline font-bold md:text-sm text-md grid justify-items-end"
            onClick={() => goBackToPage()}
          >
            ←Go Back
          </a>
        ) : null}
      </main>
    </Layout>
  );
};
export default WithLocation(Schools);
