import React, { useState } from "react";
import { navigate } from "gatsby";
import { Label } from "../components/inputs/Label";
import { Button } from "../components/Button";
import Checkbox from "../components/inputs/Checkbox";
import Layout from "../components/layout";
import Helmet from "react-helmet";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import axios from "axios";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";

const Section = ({ title, children }) => {
  return <section className="flex w-full flex-col gap-2">{children}</section>;
};
const Component = ({ name, children, isRequired }) => {
  return (
    <>
      <Label required={isRequired}>{name}</Label>
      {children}
    </>
  );
};

const Terms = () => {
  const [answers, setAnswers] = useState();
  const [msg, SetMsg] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [disableBtn, SetdisableBtn] = useState(false);
  const { isUserLogedIn, setpageFormUrl, userForm, setUserForm } = useUserStore(
    (state) => ({
      setpageFormUrl: state.setpageFormUrl,
      setUserForm: state.setUserForm,
      userForm: state.userForm,
      isUserLogedIn: state.isUserLogedIn,
    })
  );

  const setAnswer = (questionId, answer) => {
    setAnswers({ ...answers, [questionId]: answer });
  };

  const termsFormSubmit = async (event) => {
    event.preventDefault();
    SetdisableBtn(true);
    setShowModal(true);
    const formUser = { ...userForm, AcceptTermsCondition: "Y" };
    const bodyFormData = new FormData();
    bodyFormData.append("sbr", JSON.stringify(formUser));
    await axios({
      method: "post",
      url: process.env.API_URL + "PostAcceptTerms",
      data: bodyFormData,
      headers: { "Content-Type": "multipart/form-data" },
    })
      .then((res) => {
        setShowModal(false);
        if (res.data.ReturnMessage.toUpperCase() === "SUCCESS") {
          SetdisableBtn(false);
          setUserForm(res.data);
          setpageFormUrl(res.data.PageName);
          navigate(`/dashboard`);
        } else if (
          res.data.ReturnMessage.toUpperCase() === "FAILEDVALIDATION"
        ) {
          SetdisableBtn(false);
          const errApi = res.data.QuestionErrorList.map((err, idx) => (
            <p key={idx}>{err.ErrorMsg}</p>
          ));
          SetMsg(errApi);
          setShowModal(false);
          return;
        } else {
          SetdisableBtn(false);
          SetMsg("Server error has occured.");
          return;
        }
      })
      .catch((err) => {
        SetdisableBtn(false);
        SetMsg("Server error has occured.");
        return;
      });
  };
  React.useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
    };
    fetchData();
  }, []);
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={"Terms and Conditions"}
      />
      <div className="w-full space-y-5 bg-white p-7 md:p-11 md:max-w-3xl md:shadow-md md:shadow-light-purple mt-12 md:mt-6">
        {showModal ? <ProcessingModal /> : null}
        <form onSubmit={(event) => termsFormSubmit(event)}>
          <h1
            className="md:text-xl text-lg font-bold"
            tabindex="-1"
            id="GoBackBtn1"
          >
            College Board Terms of Service for CSS Profile® and IDOC (Consumer)
          </h1>
          <ol className="space-y-5 mt-3 mb-2">
            <li>
              You are permitted to access and use the features and functionality
              of INSTITUTIONAL DOCUMENTATION SERVICE ("IDOC") or CSS PROFILE®
              ("CSS Profile") solely to provide financial information or
              documentation to an institution or scholarship program seeking to
              process your application for financial aid and solely in
              accordance with the user information provided online and by your
              institution or scholarship program(s) ("Permitted Use"). "You" or
              "you" means a student or their parent(s) or other legal
              guardian(s).
            </li>
            <li>
              By clicking the "Agree" button (CSS Profile) or the acceptance box
              (IDOC), You agree that you have read and understand these Terms of
              Service and that your use of the service conforms to them. If You
              do not agree to these Terms of Service, DO NOT click on the
              "Agree" button (CSS Profile) or the acceptance box (IDOC) and You
              will not be able to proceed to use the websites for the
              service(s).You understand that College Board is not granting to
              You any license or sublicense to IDOC or CSS Profile , and that
              You are granted only limited rights of online access to use the
              features and functionality of these services.
            </li>
            <li>
              You understand that your application or information provided to an
              institution or scholarship program(s) via IDOC or CSS Profile does
              not provide or automatically generate a final determination, or
              actual award, of financial assistance. College Board has no
              responsibility or liability in connection with any financial
              assistance or award that You may or may not receive.
            </li>
            <li>
              College Board will only share the information You provide in CSS
              Profile with the institutions or scholarship program(s) that You
              select.  The institutions You select will receive the information
              from your application they requested. Not all institutions request
              all information. When you use a CSS Profile Fee Waiver to complete
              your CSS Profile, College Board will advise the institution or
              scholarship program(s) receiving your information that You have
              used a CSS Profile Fee Waiver. If an institution or scholarship
              program has provided You with a payment code to use for the CSS
              Profile fee, the institution or scholarship program(s) that gave
              You the payment code will know whether You used it to send your
              information to that institution or scholarship program. Some
              institutions may elect to not receive certain documents via IDOC.
              If You have uploaded a document that the institution has selected
              not to receive, that institution will not receive the document via
              IDOC.
            </li>
            <li>
              College Board will have no responsibility or liability in
              connection with any loss or damage which may be incurred by You,
              your parents or legal guardians as a result of:
              <ol
                start="1"
                type="a"
                className="ml-8 mt-2"
                style={{ listStyleType: "lower-alpha" }}
              >
                <li>
                  Invalid or insufficient data that You, your parents or legal
                  guardians enter or upload; or
                </li>
                <li>
                  Any course of action taken by You, your parents or legal
                  guardians in reliance on your estimated financial assistance;
                  or
                </li>
                <li>
                  Any deadlines set by an institution which You, your parents or
                  legal guardians miss; or
                </li>
                <li>
                  Any communications from your institution or generated by IDOC
                  or CSS Profile that You, your parents or legal guardians
                  delete, do not respond to or otherwise fail to adhere to the
                  instructions provided; or
                </li>
                <li>
                  Your use of the IDOC or CSS Profile services other than in
                  accordance with the Permitted Use.
                </li>
              </ol>
            </li>
            <li>
              You agree not to allow any person to use your account (other than
              an agent acting on your behalf) to access any features of the
              services that may require registration. College Board retains the
              right to deny access to anyone who we believe has violated these
              Terms of Service.
            </li>
            <div style={{ marginLeft: "-15px" }}>
              You shall not use the service, allow others to use the services,
              or knowingly condone use of the services by others, in any manner
              that attempts to, or is likely to gain unauthorized access to the
              services, other users' accounts, names, passwords, and user
              information.
            </div>
            <li>
              When You begin your CSS Profile application You will be asked to
              include your preferred first name. Your preferred first name will
              then be used throughout the application, will be seen by anyone
              who logs into your CSS Profile application such as your parent(s)
              or legal guardian(s), will be shared with any institutions that
              receive your profile application, and will be used in future CSS
              Profile communications. Do not share your preferred first name
              with us if You do not want this information to be shared with
              institutions, or visible in the CSS Profile application.
            </li>
            <li>
              You will access IDOC and CSS Profile through a College Board
              website. You understand that there is a risk of interruption to
              websites. You understand that College Board accepts no
              responsibility for security of information transmitted over the
              Internet. College Board engages third parties (“Service
              Provider(s)”) to assist in processing CSS Profile and IDOC
              applications. The Service Providers, including , FolderWave, Inc.,
              Recordsforce, Inc, Microsoft Azure, DocuSign Inc., Cerburus FTP
              Server, FIS Worldpay, Twilio SendGrid, ContactUS and Alorica Inc.,
              are engaged for the sole purpose of providing the following
              services:
              <br />
              <ol
                start="1"
                type="a"
                className="ml-8 mt-2"
                style={{ listStyleType: "lower-alpha" }}
              >
                <li>
                  FolderWave, Inc. develops and maintains the system which
                  collects documentation and information from applicants and
                  their families relevant to financial aid need analysis, and
                  then makes such information available to the
                  applicant-designated institution on a secure website and via
                  secure data transfer.
                </li>
                <li>
                  Recordsforce, Inc. provides document processing services
                  including the scanning and keying of paper documents and
                  electronically uploaded documentation provided via the IDOC
                  consumer interface.
                </li>
                <li>
                  Microsoft Azure is a sub-contractor of FolderWave, Inc. that
                  provides hosting services for the system.
                </li>
                <li>
                  Cerberus FTP Server is a sub-contractor of FolderWave, Inc.
                  that allows for a secure transfer of data to institutions and
                  programs.
                </li>
                <li>
                  Twilio SendGrid is a sub-contractor of FolderWave, Inc. that
                  allows email communication to applicants, institutions and
                  programs.
                </li>
                <li>
                  FIS Worldpay is a company that College Board uses to process
                  your credit card payments.
                </li>
                <li>
                  DocuSign Inc. allows IDOC users to fill out certain required
                  forms online and apply an e-Signature to the form.
                </li>
                <li>
                  Alorica Inc. And ContactUS provide College Board with customer
                  support services.
                </li>
              </ol>
            </li>
            <li>
              You understand that all title and proprietary rights in and to
              IDOC and CSS Profile are owned by College Board, including
              copyright, trade secrets and trademarks. Other than solely in
              connection with the Permitted Use, You are prohibited from
              copying, disseminating, publishing, displaying or distributing in
              any form, or reproducing the content in whole or in part. You also
              agree that You will not, and will not encourage anyone else to:
              copy, decompile, reverse engineer, disassemble, attempt to derive
              the source code, data layouts, algorithms and/or formulas of,
              decrypt, modify, or create derivative works of the IDOC or CSS
              Profile systems, system updates or any part.
            </li>
            <li>
              College Board may use data and information you provide in CSS
              Profile or IDOC to maintain, develop, support, improve, and
              diagnose CSS PROFILE and IDOC.
            </li>
            <li>
              <strong>GENERAL DISCLAIMER.</strong> CCOLLEGE BOARD AND ITS
              AFFILIATES MAKE NO WARRANTIES, WHETHER EXPRESS OR IMPLIED,
              REGARDING COLLEGE BOARD SERVICES INCLUDING WITHOUT LIMITATION THAT
              USING CSS PROFILE OR IDOC WILL MEET YOUR REQUIREMENTS, BE
              UNINTERRUPTED OR ERROR-FREE OR PROVIDE YOUR DESIRED RESULT. YOU
              ACCEPT COLLEGE BOARD SERVICES AS IS.
            </li>
            <li>
              <strong>LIMITATION OF LIABILITY.</strong>
              EXCEPT TO THE EXTENT FINALLY DETERMINED TO BE PROHIBITED BY LAW,
              COLLEGE BOARD’S TOTAL LIABILITY TO YOU, YOUR PARENTS OR LEGAL
              GUARDIANS OR ANYONE CLAIMING BY OR THROUGH YOU OR ON YOUR BEHALF,
              FOR ANY CLAIMS, LOSSES, COSTS, OR DAMAGES ARISING OUT OF OR
              RESULTING FROM OR IN ANY WAY RELATED TO COLLEGE BOARD OR COLLEGE
              BOARD SERVICES, FROM ANY CAUSE, SHALL NOT EXCEED $100.00. IN
              ADDITION, COLLEGE BOARD WILL NOT BE LIABLE IN ANY EVENT FOR ANY
              CONSEQUENTIAL, INDIRECT, PUNITIVE, EXEMPLARY, OR SPECIAL DAMAGES.
            </li>

            <li>
              Other than disputes involving infringement of College Board's
              intellectual property rights, all disputes against College Board
              and/or any or all of its contractors, that relate in any way to
              CSS Profile or IDOC, shall exclusively be resolved by a single
              arbitrator through binding, individual arbitration administered by
              the American Arbitration Association (“AAA”), under the AAA
              Consumer Arbitration Rules in effect at the time a request for
              arbitration is filed with the AAA. Copies of the AAA Rules can be
              located at{" "}
              <a
                target="_blank"
                className="aStyle"
                href="https://na01.safelinks.protection.outlook.com/?url=https%3A%2F%2Furldefense.proofpoint.com%2Fv2%2Furl%3Fu%3Dhttps-3A__na01.safelinks.protection.outlook.com_-3Furl-3Dhttp-253A-252F-252Fwww.adr.org-26data-3D02-257C01-257Cpschwartz-2540collegeboard.org-257Cc7e0e2f7e58d4872f50908d5a3e09186-257C7530bdedfd6e4f58b5d2ea681eb07663-257C0-257C0-257C636595107281541883-26sdata-3DfTTna7kb3HszFkB0poMRzVbVzE5x5nszwluh-252Bkl9jlc-253D-26reserved-3D0%26d%3DDwMFAg%26c%3DptMoEJ5oTofwe4L9tBtGCQ%26r%3Dox-mXONU3vbhmbFSFcqGQl-bbMlP8Hdwx2iWpOp6ZWk%26m%3DmqUeAv7Ex7ExaLqmx5eofd3ugsG4ptMEL_V93kaCG74%26s%3DBdHvqsIJgknfcCE5227e1RznJxyk4G2ZBlq01Igqapk%26e%3D&data=02%7C01%7Cpschwartz%40collegeboard.org%7C79038895ce5446a7d4ad08d5a538bbbf%7C7530bdedfd6e4f58b5d2ea681eb07663%7C0%7C0%7C636596585456865834&sdata=C1kSgy9x3EiJnCgRz7ed4asJE7sO%2FkEQqbSMEweqP4k%3D&reserved=0"
              >
                adr.org.
              </a>
               Unless the parties mutually agree otherwise, the seat and the
              place of the arbitration shall be New York, New York. The parties
              agree that the Federal Arbitration Act (“FAA”), 9 U.S.C. § 1 et
              seq. governs this provision, and it is the intent of the parties
              that the FAA shall pre-empt all State laws to the fullest extent
              permitted by law. No arbitration may be maintained as a class
              action, and the arbitrator shall not have the authority to combine
              or aggregate the disputes of more than one individual, conduct any
              class proceeding, make any class award, or make an award to any
              person or entity not a party to the arbitration, without the
              express written consent of College Board. By agreeing to
              arbitration in accordance with this section, You, your parents and
              legal guardians are waiving your rights to have your dispute heard
              by a judge or jury. Each party will be responsible for its own
              fees and expenses incurred in connection with the arbitration,
              regardless of its outcome. For purposes of this provision, each
              College Board contractor is a third-party beneficiary of this
              section, is entitled to the rights and benefits hereunder, and may
              enforce the provisions hereof as if it were a party hereto.
            </li>
            <li>
              IDOC or CSS Profile services may be taken down periodically for
              scheduled maintenance and technical fixes.
            </li>
            <li>
              For Users outside the United States: IDOC and CSS Profile operate
              on a software as a service platform that is located in the United
              States. Therefore, your information will be transferred from your
              location to the United States. When You furnish information to
              College Board's IDOC or CSS Profile services, You are consenting
              to a cross-border transfer of that information. You agree to
              comply with all local rules regarding online conduct and
              acceptable content. Specifically, you agree to comply with all
              applicable laws regarding the transmission of data or information
              exported from the United States or the country in which you
              reside. If You choose not to provide your information, please
              notify the institution(s) requesting your information.
            </li>
            <li>
              You agree and understand that College Board's Terms of Service
              located at{" "}
              <a
                target="_blank"
                className="aStyle"
                href="https://privacy.collegeboard.org/student-legal-terms?navId=gf-stterms"
              >
                Legal Terms for Students - College Board Privacy Center
              </a>{" "}
               also apply to your use of IDOC and CSS Profile.
            </li>
            <li>
              You agree You will not use services designed by external parties
              to allow automated, manual or other means to obtain, harvest or
              aggregate any data, software, code or other content from CSS
              Profile and/or IDOC including, without limitation –
              <ol
                start="1"
                type="a"
                className="ml-8"
                style={{ listStyleType: "lower-alpha" }}
              >
                <li>
                  Manual or automated software, devices, scripts robots, other
                  means or processes to access, "scrape", "crawl" or "spider"
                  the Services or any related data or information
                </li>
                <li>
                  Bots or other automated methods to access the Services, add or
                  download contacts, send or redirect messages
                </li>
              </ol>
              College Board reserves the right to monitor and/or restrict such
              services if we believe that they have been used to access our
              site. You further acknowledge and agree that College Board shall
              not be responsible or liable, directly or indirectly, for any
              damage or loss caused or alleged to be caused by or in connection
              with your use of or reliance on any such services available on or
              through any such site or resource.
            </li>
          </ol>
          <Section>
            <Component name="">
              <Checkbox
                required="true"
                onChange={(checked) => {
                  setAnswer("AcceptTermsCondition", checked);
                }}
                name="AcceptTermsCondition"
                label="I understand and agree to these Terms of Service, which govern my use of the IDOC service and CSS Profile online forms"
              />
            </Component>
            <Button disabled={disableBtn} className="w-full md:w-fit">
              Continue
            </Button>
          </Section>
        </form>
        {msg ? <ValidationError message={msg} /> : null}
      </div>
    </Layout>
  );
};
export default WithLocation(Terms);
