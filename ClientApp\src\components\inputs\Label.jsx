import React from "react"
import parse from "html-react-parser"

export function Label({
  required,
  children,
  disabled,
  className = "",
  labelFormat,
  ...props
}) {
  const labelContent =
    typeof children === "string"
      ? children + (required ? " (required)" : "")
      : children
  const textColor = disabled ? "text-gray-600" : "text-black"

  const isString = thing =>
    Object.prototype.toString.call(thing) === "[object String]"

  return (
    <>
      {labelFormat ? (
        <legend
          className={`cursor-pointer font-bold ${textColor} ${className}`}
          {...props}
        >
          {isString(labelContent) ? parse(labelContent) : labelContent}
        </legend>
      ) : (
        <label
          className={`cursor-pointer font-bold ${textColor} ${className}`}
          {...props}
        >
          {isString(labelContent) ? parse(labelContent) : labelContent}
        </label>
      )}
    </>
  )
}
