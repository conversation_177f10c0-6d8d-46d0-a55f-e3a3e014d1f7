import React from "react";
import { Label } from "../inputs/Label";
import { focusWithinStyles } from "./inputs";

export function InputBox({
  label,
  checked,
  children,
  disabled,
  error,
  required,
}) {
  const bgColorClass = bgColor(disabled, checked);
  let msg = required ? " (required)" : "";
  return (
    <Label
      disabled={disabled}
      className={`flex rounded-sm border-2 border-b-4 py-3.5 px-6 hover:cursor-pointer ${focusWithinStyles} ${bgColorClass} ${
        error ? "border-rose-600" : "border-black"
      }`}
    >
      <div>{children}</div>
      <span
        className="break-words"
        dangerouslySetInnerHTML={{ __html: label + msg }}
      />
    </Label>
  );
}

function bgColor(disabled, checked) {
  if (disabled && checked) {
    return "bg-gray-300";
  } else if (disabled) {
    return "bg-gray-100";
  } else if (checked) {
    return "bg-sky-200";
  } else {
    return "bg-white";
  }
}
